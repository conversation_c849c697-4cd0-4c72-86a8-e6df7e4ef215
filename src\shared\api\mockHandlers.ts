/* eslint-disable */
import { http, HttpResponse } from 'msw';
import { delay } from 'shared/lib/delay';

// Mock data imports
import {
  packageDefTableData,
  packageFilesData,
  packageFilesColumns,
  requestItemsData,
  requestItemsColumns,
} from './mockData';
import {
  eppTreeMockData,
  krgTreeRootMockData,
} from './mockData/eppTreeMockData';
import { krg3RequestNoticeMockData } from './mockData/krg3RequestNoticeMockData';
import { krgTableMockData } from './mockData/krgTableMockData';
import { krg3NoticeForRequestMockData } from './mockData/notices';
import {
  assDocs,
  filenetMainFindDocuments,
  filenetPublicationGet,
  filenetRubrics,
  rubricTree,
} from './mockData/filenet-main-find-documents';
import { krg4CreationPlan } from './mockData/u-krg-table-mock-data';

// Constants
const DEFAULT_DELAY = 500;

// ============================================================================
// INTERACTIVE MOCK DATA GENERATOR - Random Directory Catalogs
// ============================================================================

/**
 * Хранилище информации о каталогах и их ожидаемом количестве файлов Ключ:
 * itemId каталога, Значение: totalCountOfLeafs
 */
const catalogFilesRegistry = new Map<string, number>();

/**
 * 🎲 ИНТЕРАКТИВНЫЙ ГЕНЕРАТОР СЛУЧАЙНЫХ КАТАЛОГОВ
 *
 * Этот генератор автоматически создает случайные каталоги директорий для любых
 * неизвестных itemId в запросах к /kzid_rest/krg/epc-tree
 *
 * 📋 ПАРАМЕТРЫ ЗАПРОСА:
 *
 * - ItemsCount: количество элементов (по умолчанию 10)
 * - Scenario: сценарий генерации • 'mixed' - смешанные файлы и директории (по
 *   умолчанию) • 'files-only' - только файлы • 'dirs-only' - только директории
 *   • 'deep-nested' - глубокая вложенность (80% директорий)
 *
 * 🔧 ПРИМЕРЫ ИСПОЛЬЗОВАНИЯ:
 *
 * 1. Базовый запрос (10 смешанных элементов): GET
 *    /kzid_rest/krg/epc-tree?itemId=новый-uuid
 * 2. Только файлы (20 штук): GET
 *    /kzid_rest/krg/epc-tree?itemId=новый-uuid&scenario=files-only&itemsCount=20
 * 3. Только директории (5 штук): GET
 *    /kzid_rest/krg/epc-tree?itemId=новый-uuid&scenario=dirs-only&itemsCount=5
 * 4. Глубокая структура (15 элементов): GET
 *    /kzid_rest/krg/epc-tree?itemId=новый-uuid&scenario=deep-nested&itemsCount=15
 *
 * ✨ ОСОБЕННОСТИ:
 *
 * - Все itemId генерируются как уникальные UUID v4
 * - Названия каталогов и файлов реалистичные (на русском языке)
 * - Поддерживаются маркеры каталогов [t], [i], [m], [x]
 * - Случайные значения для всех полей (permissions, isFixed, disabled и т.д.)
 * - Правильная структура ключей и позиций
 * - Различные расширения файлов (.xlsx, .docx, .pdf и т.д.)
 * - КОРРЕКТНЫЙ РАСЧЕТ totalCountOfLeafs: каждая директория содержит точное
 *   количество файлов, указанное в поле totalCountOfLeafs (включая файлы во
 *   всех подкаталогах, которые будут загружены при раскрытии)
 *
 * 🔢 ЛОГИКА totalCountOfLeafs:
 *
 * - Генератор предварительно рассчитывает общее количество файлов
 * - Распределяет файлы между директориями логически корректно
 * - Каждая директория получает случайное, но реалистичное количество файлов
 * - Поле totalCountOfLeafs показывает файлы, которые будут в этой директории при
 *   следующем запросе с itemId этой директории
 *
 * 📁 ОБРАБОТКА ПУСТЫХ КАТАЛОГОВ:
 *
 * - IsLeaf = true, totalCountOfLeafs = 0 → каталог полностью пустой (нет
 *   содержимого)
 * - IsLeaf = false, totalCountOfLeafs = 0 → каталог может содержать пустые
 *   подкаталоги
 * - При запросе содержимого листового каталога возвращается пустой результат
 * - При запросе содержимого пустого не-листового каталога генерируются пустые
 *   подкаталоги
 */

/** Генератор уникальных UUID v4 */
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/** Генератор случайных названий каталогов */
function generateRandomDirectoryName(): string {
  const prefixes = [
    'Документы',
    'Материалы',
    'Отчеты',
    'Проекты',
    'Архив',
    'Реестры',
    'Уведомления',
    'Заявки',
    'Пакеты',
    'Результаты',
    'Ведомости',
    'Справки',
    'Протоколы',
    'Планы',
    'Контроль',
  ];

  const suffixes = [
    '2024',
    '2025',
    'Новые',
    'Обработанные',
    'Входящие',
    'Исходящие',
    'Временные',
    'Постоянные',
    'Рабочие',
    'Финальные',
  ];

  const markers = ['', ' [t]', ' [i]', ' [m]', ' [x]'];

  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const suffix =
    Math.random() > 0.6
      ? ` ${suffixes[Math.floor(Math.random() * suffixes.length)]}`
      : '';
  const marker = markers[Math.floor(Math.random() * markers.length)];

  return `${prefix}${suffix}${marker}`;
}

/** Генератор случайных названий файлов */
function generateRandomFileName(): string {
  const names = [
    'отчет',
    'документ',
    'справка',
    'протокол',
    'ведомость',
    'реестр',
    'план',
    'заявка',
    'уведомление',
    'файл',
  ];

  const extensions = ['.xlsx', '.docx', '.pdf', '.doc', '.xls'];
  const dates = ['01.01.2024', '15.03.2024', '30.06.2024', '12.12.2024'];

  const name = names[Math.floor(Math.random() * names.length)];
  const date =
    Math.random() > 0.5
      ? `_${dates[Math.floor(Math.random() * dates.length)]}`
      : '';
  const number =
    Math.random() > 0.7 ? `_${Math.floor(Math.random() * 1000) + 1}` : '';
  const ext = extensions[Math.floor(Math.random() * extensions.length)];

  return `${name}${date}${number}${ext}`;
}

/** Генератор случайного цвета для элементов дерева */
function generateRandomColor(isDirectory: boolean): string {
  if (isDirectory) {
    return 'rgba(0, 0, 0, 0.65)'; // Стандартный цвет для директорий
  } else {
    // Цвета для файлов
    const colors = [
      'rgba(0,0,255,0.5)', // Синий
      'rgba(255,0,0,0.5)', // Красный
      'rgba(0,128,0,0.5)', // Зеленый
      'rgba(255,165,0,0.5)', // Оранжевый
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }
}

/**
 * Расширенный генератор с корректным расчетом totalCountOfLeafs
 *
 * @param scenario - Сценарий генерации ('mixed', 'files-only', 'dirs-only',
 *   'deep-nested')
 * @param parentId - ID родительского элемента
 * @param itemsCount - Количество элементов
 * @param expectedTotalFiles - Ожидаемое общее количество файлов (из
 *   totalCountOfLeafs родительского каталога)
 * @returns Сгенерированные данные дерева каталогов
 */
function generateCatalogByScenario(
  scenario: 'mixed' | 'files-only' | 'dirs-only' | 'deep-nested' = 'mixed',
  parentId: string = generateUUID(),
  itemsCount: number = 10,
  expectedTotalFiles?: number,
): { treeData: TreeElement[]; history: any[]; foundNodes: any[]; debug?: any } {
  const treeData: TreeElement[] = [];

  // Если указано ожидаемое количество файлов, используем его, иначе генерируем случайно
  const totalFilesToDistribute =
    expectedTotalFiles !== undefined
      ? expectedTotalFiles
      : Math.floor(Math.random() * itemsCount * 3) + itemsCount;
  let remainingFiles = totalFilesToDistribute;

  // Если есть ожидаемое количество файлов, корректируем itemsCount для соответствия
  let actualItemsCount = itemsCount;
  if (expectedTotalFiles !== undefined) {
    if (expectedTotalFiles === 0) {
      // Специальный случай: каталог должен содержать 0 файлов
      // Но может содержать пустые подкаталоги (не листовые)
      actualItemsCount = Math.floor(Math.random() * 3) + 1; // 1-3 пустых подкаталога
      scenario = 'dirs-only'; // Только каталоги, без файлов
    } else {
      // Для корректного распределения нужно учесть сценарий
      switch (scenario) {
        case 'files-only':
          // Все элементы - файлы, поэтому itemsCount = expectedTotalFiles
          actualItemsCount = expectedTotalFiles;
          break;
        case 'dirs-only':
          // Все элементы - директории, файлы будут внутри них
          actualItemsCount = Math.min(
            itemsCount,
            Math.max(1, Math.ceil(expectedTotalFiles / 10)),
          );
          break;
        case 'mixed':
        case 'deep-nested':
        default:
          // Смешанный режим: используем исходное количество элементов
          // Файлы будут распределены между элементами для достижения expectedTotalFiles
          actualItemsCount = itemsCount;
          break;
      }
    }
  }

  // Рассчитываем оптимальное количество директорий и файлов
  let directoryCount = 0;
  let directFilesCount = 0;

  if (expectedTotalFiles !== undefined) {
    // Точный расчет для достижения expectedTotalFiles
    switch (scenario) {
      case 'files-only':
        directoryCount = 0;
        directFilesCount = actualItemsCount; // Все элементы - файлы
        break;
      case 'dirs-only':
        directoryCount = actualItemsCount; // Все элементы - директории
        directFilesCount = 0;
        break;
      case 'mixed':
      case 'deep-nested':
      default:
        if (expectedTotalFiles === 0) {
          // Пустой каталог: только директории, без файлов
          directoryCount = actualItemsCount;
          directFilesCount = 0;
        } else {
          // Смешанный режим: сбалансированное распределение
          if (expectedTotalFiles <= actualItemsCount) {
            // Если файлов мало, делаем больше прямых файлов
            directFilesCount = Math.max(
              1,
              Math.floor(expectedTotalFiles * 0.6),
            ); // 60% файлов - прямые
            directoryCount = Math.min(
              actualItemsCount - directFilesCount,
              Math.ceil((expectedTotalFiles * 0.4) / 3),
            ); // Остальные - директории, но не более чем нужно
          } else {
            // Если файлов много, делаем больше директорий
            directFilesCount = Math.min(
              actualItemsCount,
              Math.max(1, Math.floor(actualItemsCount * 0.3)),
            ); // 30% элементов - прямые файлы
            directoryCount = actualItemsCount - directFilesCount;
          }

          // Проверяем, что у нас есть хотя бы одна директория, если нужно распределить файлы
          const filesForDirectories = expectedTotalFiles - directFilesCount;
          if (filesForDirectories > 0 && directoryCount === 0) {
            // Нужна хотя бы одна директория
            directoryCount = 1;
            directFilesCount = actualItemsCount - 1;
          }
        }
        break;
    }
  } else {
    // Случайное распределение для старой логики
    for (let i = 0; i < actualItemsCount; i++) {
      let isDirectory: boolean;

      switch (scenario) {
        case 'files-only':
          isDirectory = false;
          break;
        case 'dirs-only':
          isDirectory = true;
          break;
        case 'deep-nested':
          isDirectory = i < Math.ceil(actualItemsCount * 0.8);
          break;
        case 'mixed':
        default:
          isDirectory = Math.random() > 0.4;
          break;
      }

      if (isDirectory) {
        directoryCount++;
      } else {
        directFilesCount++;
      }
    }
  }

  // directFilesCount уже рассчитан выше

  // Если есть директории, распределяем файлы между ними и прямыми файлами
  const filesPerDirectory: number[] = [];

  // Для expectedTotalFiles нужно точно распределить все файлы
  let filesForDirectories: number;
  let actualDirectFilesCount: number;

  if (expectedTotalFiles !== undefined) {
    // Точное распределение для соответствия expectedTotalFiles
    if (expectedTotalFiles === 0) {
      filesForDirectories = 0;
      actualDirectFilesCount = 0;
    } else if (directoryCount === 0) {
      // Только прямые файлы
      filesForDirectories = 0;
      actualDirectFilesCount = expectedTotalFiles;
    } else if (directFilesCount === 0) {
      // Только файлы в директориях
      filesForDirectories = expectedTotalFiles;
      actualDirectFilesCount = 0;
    } else {
      // Смешанный режим: распределяем между прямыми файлами и директориями
      actualDirectFilesCount = Math.min(
        directFilesCount,
        Math.floor(expectedTotalFiles * 0.3),
      ); // 30% прямых файлов
      filesForDirectories = expectedTotalFiles - actualDirectFilesCount;
    }
  } else {
    // Случайное распределение
    filesForDirectories = Math.max(0, remainingFiles - directFilesCount);
    actualDirectFilesCount = directFilesCount;
  }

  if (directoryCount > 0) {
    if (expectedTotalFiles === 0) {
      // Специальный случай: все директории должны быть пустыми (0 файлов)
      for (let i = 0; i < directoryCount; i++) {
        filesPerDirectory.push(0);
      }
    } else if (filesForDirectories > 0) {
      for (let i = 0; i < directoryCount; i++) {
        if (i === directoryCount - 1) {
          // Последней директории отдаем все оставшиеся файлы
          filesPerDirectory.push(filesForDirectories);
        } else {
          // Случайно распределяем файлы, но оставляем минимум для остальных директорий
          const maxForThisDir = Math.max(
            1,
            filesForDirectories - (directoryCount - i - 1),
          );
          const filesForThisDir = Math.floor(Math.random() * maxForThisDir) + 1;
          filesPerDirectory.push(filesForThisDir);
          filesForDirectories -= filesForThisDir;
        }
      }
    } else {
      // Если нет файлов для директорий, каждая получает минимум 1
      for (let i = 0; i < directoryCount; i++) {
        filesPerDirectory.push(1);
      }
    }
  }

  let directoryIndex = 0;
  let fileIndex = 0;

  // Определяем, сколько элементов нужно создать
  const totalElementsToCreate =
    expectedTotalFiles !== undefined
      ? directoryCount + directFilesCount
      : actualItemsCount;

  for (let i = 0; i < totalElementsToCreate; i++) {
    let isDirectory: boolean;

    // Строго следуем плану распределения
    if (expectedTotalFiles !== undefined) {
      // Точный контроль: создаем элементы согласно рассчитанному плану
      const remainingDirectories = directoryCount - directoryIndex;
      const remainingFiles = directFilesCount - fileIndex;
      const remainingItems = totalElementsToCreate - i;

      if (remainingDirectories === 0) {
        isDirectory = false; // Только файлы
      } else if (remainingFiles === 0) {
        isDirectory = true; // Только директории
      } else if (remainingItems === remainingDirectories) {
        isDirectory = true; // Нужно создать все оставшиеся директории
      } else if (remainingItems === remainingFiles) {
        isDirectory = false; // Нужно создать все оставшиеся файлы
      } else {
        // Случайный выбор среди допустимых вариантов
        isDirectory = Math.random() > 0.5;
      }
    } else {
      // Старая логика для случайной генерации
      switch (scenario) {
        case 'files-only':
          isDirectory = false;
          break;
        case 'dirs-only':
          isDirectory = true;
          break;
        case 'deep-nested':
          isDirectory = i < Math.ceil(actualItemsCount * 0.8);
          break;
        case 'mixed':
        default:
          isDirectory = Math.random() > 0.4;
          break;
      }
    }

    const itemId = generateUUID();
    const key = `0-0-${i}`;

    const baseItem = {
      itemId,
      parent: parentId,
      key,
      position: i,
      disabled: Math.random() > 0.8,
      checked: false,
      cabinetOnly: Math.random() > 0.9,
      tagged: false,
      marked:
        Math.random() > 0.95
          ? ['t', 'i', 'm'][Math.floor(Math.random() * 3)]
          : '',
      linked: [],
      children: [],
      isLeaf: !isDirectory,
      isDirectory,
      isCallable: false,
      color: generateRandomColor(isDirectory),
    };

    if (isDirectory) {
      const title = generateRandomDirectoryName();
      // Используем предварительно рассчитанное количество файлов
      const totalCountOfLeafs = filesPerDirectory[directoryIndex] || 0;
      directoryIndex++;

      // Определяем, является ли каталог листовым (пустым)
      // Если totalCountOfLeafs = 0, то с вероятностью 50% каталог является листом (полностью пустой)
      // Иначе он может содержать пустые подкаталоги
      const isLeafDirectory = totalCountOfLeafs === 0 && Math.random() > 0.5;

      // Сохраняем информацию о каталоге в реестр
      catalogFilesRegistry.set(itemId, totalCountOfLeafs);

      treeData.push({
        ...baseItem,
        title,
        rawTitle: title.replace(/\s*\[[^\]]*\]$/, ''),
        totalCountOfLeafs,
        isLeaf: isLeafDirectory, // Устанавливаем isLeaf только для пустых каталогов
        hasLinked: Math.random() > 0.85,
        isFixed: Math.floor(Math.random() * 4) as 0 | 1 | 2 | 3,
      });
    } else {
      const title = generateRandomFileName();

      treeData.push({
        ...baseItem,
        title,
        rawTitle: title,
        isMain: Math.floor(Math.random() * 2) as 0 | 1,
        fileNetId: itemId,
        fileSignExist: Math.random() > 0.8,
        permissions: {
          canView: Math.random() > 0.05,
          canDownload: Math.random() > 0.15,
        },
        isRemovable: Math.random() > 0.2,
      });
      fileIndex++;
    }
  }

  return {
    treeData,
    history: [],
    foundNodes: [],
    // Дополнительная отладочная информация
    debug: {
      expectedTotalFiles,
      plannedDirectFilesCount: directFilesCount,
      actualDirectFilesCount: fileIndex,
      filesForDirectories:
        expectedTotalFiles !== undefined
          ? expectedTotalFiles === 0
            ? 0
            : filesForDirectories
          : undefined,
      plannedDirectoryCount: directoryCount,
      actualDirectoryCount: directoryIndex,
    },
  };
}

// ============================================================================
// EPP TREE HANDLERS - Tree structure endpoints
// ============================================================================

/** EPP Tree handlers - Tree structure and file visibility endpoints */
catalogFilesRegistry.set('47ff68f5-896a-4fb5-a777-bc1602fe09b4', 152042);
const eppTreeHandlers = [
  http.get(
    'http://127.0.0.1:18080/kzid_rest/krg/epc-tree',
    async ({ request }) => {
      const url = new URL(request.url);
      const itemId = url.searchParams.get('itemId');
      const maxDepth = parseInt(url.searchParams.get('maxDepth') || '3', 10);
      const itemsCount = parseInt(
        url.searchParams.get('itemsCount') || '15', // Увеличиваем базовое количество
        10,
      );
      const scenario =
        (url.searchParams.get('scenario') as
          | 'mixed'
          | 'files-only'
          | 'dirs-only'
          | 'deep-nested') || 'mixed';

      // Параметры пагинации
      const page = parseInt(url.searchParams.get('page') || '1', 10);
      const size = parseInt(url.searchParams.get('size') || '10', 10); // 100 элементов на страницу

      await delay();

      // Если нет itemId, возвращаем корневые данные с пагинацией
      if (!itemId) {
        // Генерируем больше корневых элементов для демонстрации пагинации
        const extendedRootData = generateCatalogByScenario(
          'mixed',
          'root',
          1000, // 1000 корневых элементов
          undefined,
        );

        const startIndex = (page - 1) * size;
        const endIndex = startIndex + size;
        const paginatedRootData = {
          ...extendedRootData,
          treeData: extendedRootData.treeData.slice(startIndex, endIndex),
          pagination: {
            total: extendedRootData.treeData.length,
            page: page,
            size: size,
          },
        };
        return HttpResponse.json(paginatedRootData, { status: 200 });
      }

      // // Стандартные данные для известного itemId
      // if (itemId === '47ff68f5-896a-4fb5-a777-bc1602fe09b4') {
      //   return HttpResponse.json(eppTreeMockData, { status: 200 });
      // }

      // Для любых других itemId автоматически генерируем случайные каталоги

      // Проверяем, есть ли информация о том, сколько файлов должно быть в этом каталоге
      const expectedTotalFiles = catalogFilesRegistry.get(itemId);

      // Если ожидается 0 файлов, нужно проверить, является ли каталог листовым
      if (expectedTotalFiles === 0) {
        // Проверяем в существующих данных, является ли этот каталог листовым
        const existingData = eppTreeMockData.treeData.find(
          (item) => item.itemId === itemId,
        );
        if (existingData?.isLeaf) {
          return HttpResponse.json(
            {
              treeData: [],
              history: [],
              foundNodes: [],
            },
            { status: 200 },
          );
        }
      }

      // Генерируем полный набор данных с большим количеством элементов
      // Специальная логика для демонстрации пагинации
      let totalItemsToGenerate;

      // Если это первый уровень каталогов, создаем много элементов
      if (!expectedTotalFiles) {
        totalItemsToGenerate = 1000; // 1000 элементов в каталогах
      } else {
        totalItemsToGenerate = 1000; // 1000 элементов везде
      }

      const fullRandomData = generateCatalogByScenario(
        scenario,
        itemId,
        totalItemsToGenerate,
        expectedTotalFiles,
      );

      // Применяем пагинацию
      const startIndex = (page - 1) * size;
      const endIndex = startIndex + size;
      const paginatedTreeData = fullRandomData.treeData.slice(
        startIndex,
        endIndex,
      );

      const randomData = {
        ...fullRandomData,
        treeData: paginatedTreeData,
        pagination: {
          total: fullRandomData.treeData.length,
          page: page,
          size: size,
        },
      };

      // Логируем информацию о пагинации для отладки
      console.log(`📄 Пагинация каталога ${itemId}:`, {
        page,
        size,
        totalGenerated: fullRandomData.treeData.length,
        returned: randomData.treeData.length,
        hasMore: randomData.treeData.length < fullRandomData.treeData.length,
      });

      return HttpResponse.json(randomData, { status: 200 });
    },
  ),

  // File visibility toggle
  http.put(
    'http://127.0.0.1:18080/kzid_rest/krg3_file_data/visibility',
    async () => {
      await delay(0);
      return new HttpResponse(null, { status: 204 });
    },
  ),
];

const krg3MainHandlers = [
  // Basic KRG3 table
  http.post('http://127.0.0.1:18080/kzid_rest/krg3', async ({ request }) => {
    const url = new URL(request.url);
    const page = url.searchParams.get('page') || '1';
    const size = url.searchParams.get('size') || '10';

    await delay(DEFAULT_DELAY);
    return HttpResponse.json(
      {
        ...krgTableMockData,
        page: parseInt(page, 10),
        size: parseInt(size, 10),
      },
      { status: 200 },
    );
  }),

  // Package definition table with lazy tabs
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_input_package_def',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = url.searchParams.get('pageNumber') || '1';
      const pageSize = url.searchParams.get('pageSize') || '10';

      await delay(DEFAULT_DELAY);
      return HttpResponse.json(
        {
          ...packageDefTableData,
          pagination: {
            total: 3,
            pageSize: parseInt(pageSize, 10),
          },
          pageNumber: parseInt(pageNumber, 10),
        },
        { status: 200 },
      );
    },
  ),

  // Request notice table with lazy tabs
  http.post(
    'http://127.0.0.1:18080/kzid_rest/krg3_request_notice',
    async ({ request }) => {
      const url = new URL(request.url);
      const pageNumber = url.searchParams.get('pageNumber') || '1';
      const pageSize = url.searchParams.get('pageSize') || '10';

      await delay(DEFAULT_DELAY);
      return HttpResponse.json(
        krg3RequestNoticeMockData(pageNumber, pageSize),
        { status: 200 },
      );
    },
  ),
];

/**
 * Combined mock handlers for MSW Organized by functionality for better
 * maintainability
 */
export const handlers = [
  // Tree structure endpoints
  ...krg3MainHandlers,
  ...eppTreeHandlers,
];
