import { AsyncThunk, createAsyncThunk } from '@reduxjs/toolkit';
import axios, { AxiosInstance, AxiosError } from 'axios';
// eslint-disable-next-line boundaries/element-types
import { AppDispatch, RootState } from 'processes/store';
import { appInstance } from 'shared/api';
import { ErrorWithoutShow } from 'shared/model';
import { appErrorNotification } from './appErrorNotification';

export const createAppAsyncThunk = createAsyncThunk.withTypes<{
  dispatch: AppDispatch;
  state: RootState;
}>();

interface ErrorOptions {
  errorTitle?: string;
}

type BodyParams<BodyType> = { body: BodyType; url: Endpoint };

export const createBasicPostThunk = <ReturnValue, BodyType = void>(
  name: string,
  instance: AxiosInstance = appInstance,
  errorOptions: ErrorOptions = {},
): AsyncThunk<ReturnValue, BodyParams<BodyType>, { rejectValue: AxiosError }> =>
  createAppAsyncThunk<
    ReturnValue,
    BodyParams<BodyType>,
    { rejectValue: AxiosError }
  >(
    name,
    async ({ url, body }, { rejectWithValue }) => {
      try {
        const { data } = await instance.post<ReturnValue>(url, body);

        return data;
      } catch (err) {
        if (axios.isAxiosError(err)) {
          if (errorOptions.errorTitle) {
            appErrorNotification(errorOptions.errorTitle, err as AppError);
          }
          return rejectWithValue(err);
        }
        throw err;
      }
    },
    {
      ...(errorOptions.errorTitle && {
        serializeError() {
          return new ErrorWithoutShow();
        },
      }),
    },
  );

export const createBasicGetThunk = <ReturnValue>(
  name: string,
  instance: AxiosInstance = appInstance,
  errorOptions: ErrorOptions = {},
): AsyncThunk<ReturnValue, Endpoint, { rejectValue: AxiosError }> =>
  createAppAsyncThunk<ReturnValue, Endpoint, { rejectValue: AxiosError }>(
    name,
    async (url, { rejectWithValue }) => {
      try {
        const { data } = await instance.get<ReturnValue>(url);
        return data;
      } catch (err) {
        if (axios.isAxiosError(err)) {
          if (errorOptions.errorTitle) {
            appErrorNotification(errorOptions.errorTitle, err as AppError);
          }
          return rejectWithValue(err);
        }
        throw err;
      }
    },
    {
      ...(errorOptions.errorTitle && {
        serializeError() {
          return new ErrorWithoutShow();
        },
      }),
    },
  );

export const createBasicDeleteThunk = <ReturnValue>(
  name: string,
  instance: AxiosInstance = appInstance,
  errorOptions: ErrorOptions = {},
): AsyncThunk<ReturnValue, Endpoint, { rejectValue: AxiosError }> =>
  createAppAsyncThunk<ReturnValue, Endpoint, { rejectValue: AxiosError }>(
    name,
    async (url, { rejectWithValue }) => {
      try {
        const { data } = await instance.delete<ReturnValue>(url);
        return data;
      } catch (err) {
        if (axios.isAxiosError(err)) {
          if (errorOptions.errorTitle) {
            appErrorNotification(errorOptions.errorTitle, err as AppError);
          }
          return rejectWithValue(err);
        }
        throw err;
      }
    },
    {
      ...(errorOptions.errorTitle && {
        serializeError() {
          return new ErrorWithoutShow();
        },
      }),
    },
  );

export const createBasicPutThunk = <ReturnValue, BodyType = void>(
  name: string,
  instance: AxiosInstance = appInstance,
  errorOptions: ErrorOptions = {},
): AsyncThunk<ReturnValue, BodyParams<BodyType>, { rejectValue: AxiosError }> =>
  createAppAsyncThunk<
    ReturnValue,
    BodyParams<BodyType>,
    { rejectValue: AxiosError }
  >(
    name,
    async ({ url, body }, { rejectWithValue }) => {
      try {
        const { data } = await instance.put<ReturnValue>(url, body);

        return data;
      } catch (err) {
        if (axios.isAxiosError(err)) {
          if (errorOptions.errorTitle) {
            appErrorNotification(errorOptions.errorTitle, err as AppError);
          }
          return rejectWithValue(err);
        }

        throw err;
      }
    },
    {
      ...(errorOptions.errorTitle && {
        serializeError() {
          return new ErrorWithoutShow();
        },
      }),
    },
  );

export const createBasicGetThunkWithUrl = <ReturnValue>(
  name: string,
  url: Endpoint,
  instance: AxiosInstance = appInstance,
  errorOptions: ErrorOptions = {},
): AsyncThunk<ReturnValue, void, { rejectValue: AxiosError }> =>
  createAppAsyncThunk<ReturnValue, void, { rejectValue: AxiosError }>(
    name,
    async (_, { rejectWithValue }) => {
      try {
        const { data } = await instance.get<ReturnValue>(url);

        return data;
      } catch (err) {
        if (axios.isAxiosError(err)) {
          if (errorOptions.errorTitle) {
            appErrorNotification(errorOptions.errorTitle, err as AppError);
          }
          return rejectWithValue(err);
        }

        throw err;
      }
    },
    {
      ...(errorOptions.errorTitle && {
        serializeError() {
          return new ErrorWithoutShow();
        },
      }),
    },
  );
