import { notification } from 'antd';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useUnmount } from 'react-use';
import {
  TogglePopup,
  WorkGroupEPCConfig,
  WorkGroupEPCStore,
} from 'widgets/WorkGroupEPC';
import { newlazyTree } from 'features/NewLazyTree';
import { apiUrls } from 'shared/api';
import {
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
  useTreeSearchArrow,
} from 'shared/model';

type UseTreeSearch = {
  abortSearch: Callback;
  arrowDown: Callback;
  arrowUp: Callback;
  closeEditModal: Callback;
  currentIndex: number;
  formFields: SearchFormField[];
  formValues: Record<string, string>;
  foundItemId: string;
  handleResetSearchTags: Callback;
  handleSearch: (body: { key?: Key; value?: string }) => Promise<void>;
  setFormValues: React.Dispatch<React.SetStateAction<Record<string, string>>>;
  taggedNodes: TreeElement[];
  treeRef: React.MutableRefObject<null>;
};

const isEmptyRootTree = (tree: TreeElement[]): boolean =>
  tree?.length > 0 && tree[0]?.children?.length === 0;

export const useTreeSearch = ({
  cabinetId,
  popup,
  togglePopup,
}: {
  cabinetId: string;
  popup: Record<keyof typeof popupsInitial, boolean>;
  togglePopup: TogglePopup;
}): UseTreeSearch => {
  const {
    EPC_TREE_KEY,
    popupsInitial, // eslint-disable-line
    formFields: mainFormFields,
  } = WorkGroupEPCConfig;
  const dispatch = useAppDispatch();
  const { updateFilteredNodes } = useCreateSliceActions(
    newlazyTree.reducers.slice.actions,
  );
  const entities = useAppSelector((state) =>
    newlazyTree.selectors.entitiesSelector(state, {
      treeKey: EPC_TREE_KEY,
    }),
  );

  const handleResetSearchTags = useCallback(() => {
    updateFilteredNodes({
      filter: (node: TreeElement): boolean => !!node.tagged,
      update: (node: TreeElement): void => {
        node.tagged = false;
      },
      treeKey: EPC_TREE_KEY,
    });

    togglePopup('search');
  }, [EPC_TREE_KEY, togglePopup, updateFilteredNodes]);

  const { linkedData } = useAppSelector(
    WorkGroupEPCStore.selectors.epcSelector,
  );

  const closeEditModal = useCallback(() => {
    if (popup.watch && linkedData.editLinked.length === 0) {
      togglePopup('watch');
    }
    togglePopup('edit');
  }, [linkedData.editLinked.length, popup.watch, togglePopup]);

  const selectedNodeId = useAppSelector((state) =>
    newlazyTree.selectors.selectedNodeIdSelector(state, {
      treeKey: EPC_TREE_KEY,
    }),
  );

  const abortControllerRef = useRef<AbortController | null>(null);

  const handleSearch = async (body: {
    key?: Key;
    value?: string;
  }): Promise<void> => {
    // Отменяем предыдущий запрос, если он существует
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Создаем новый контроллер для текущего запроса
    abortControllerRef.current = new AbortController();

    updateFilteredNodes({
      filter: (node: TreeElement): boolean => !!node.tagged,
      update: (node: TreeElement): void => {
        node.tagged = false;
      },
      treeKey: EPC_TREE_KEY,
    });
    try {
      const res = await dispatch(
        newlazyTree.thunks.getTreeThunk({
          body: 'marks' in body ? { ...body, isMainFile: true } : body,
          queryParams: {
            ...(entities[selectedNodeId]?.isDirectory && {
              itemId: selectedNodeId,
            }),
            cabinetId,
            isCabinet: true,
          },
          endpoint: apiUrls.workGroup.EPC.searchLazyTree,
          isSearch: true,
          treeKey: WorkGroupEPCConfig.EPC_TREE_KEY,
          signal: abortControllerRef.current.signal,
          transformResponse: WorkGroupEPCConfig.transformResponse,
        }),
      ).unwrap();

      if (isEmptyRootTree(res)) {
        notification.warn({ message: 'Поиск не дал результатов' });
      }

      togglePopup('search');
    } finally {
      abortControllerRef.current = null;
    }
  };

  // Очищаем контроллер при размонтировании компонента
  useUnmount(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  });

  const abortSearch = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current?.abort();
      abortControllerRef.current = null;
      notification.warn({ message: 'Поиск отменен' });
    }
  }, []);

  const [formValues, setFormValues] = useState<Record<string, string>>({});
  const additionalSelects = WorkGroupEPCStore.hooks.useGetSearchSelects();
  const treeData = useAppSelector((state) =>
    newlazyTree.selectors.treeSelector(state, {
      treeKey: EPC_TREE_KEY,
    }),
  );
  const [treeRef, taggedNodes, currentIndex, foundItemId, arrowUp, arrowDown] =
    useTreeSearchArrow(treeData, '', () => {
      /** TODO: Переписать */
    });
  const formFields = useMemo(
    () => [...mainFormFields, ...additionalSelects],
    [additionalSelects, mainFormFields],
  );

  return {
    abortSearch,
    treeRef,
    formFields,
    formValues,
    handleResetSearchTags,
    handleSearch,
    closeEditModal,
    setFormValues,
    taggedNodes,
    currentIndex,
    foundItemId,
    arrowDown,
    arrowUp,
  };
};
