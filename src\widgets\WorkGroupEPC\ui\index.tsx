import { Tree } from 'antd';
import classNames from 'classnames';
import type { FC } from 'react';
import { useState } from 'react';
import { useMeasure } from 'react-use';
import AutoSizer from 'react-virtualized-auto-sizer';
import {
  WorkGroupEPCStore,
  WorkGroupEPCConfig,
  WorkGroupEPCProps,
  EPCInnerProps,
} from 'widgets/WorkGroupEPC';
import { newlazyTree } from 'features/NewLazyTree';
import { SearchDrawer } from 'features/SearchDrawer';
import { usePopupsToggle } from 'shared/model';
import {
  ApiContainer,
  AppPopup,
  ButtonsContainer,
  TreeSearch,
} from 'shared/ui';
import { ENDPOINTS, EPC_TREE_KEY } from '../config';
import { CopyModalWithTree } from './CopyModalWithTree';
import { EditModal } from './EditModal';
import { EPCDownloadModal } from './EPCDownloadModal';
import styles from './styles.module.scss';
import { VisibilityFilesModal } from './VisibilityFilesModal';
import { WatchModal } from './WatchModal';

const { popupsInitial } = WorkGroupEPCConfig;

const EPCInner: FC<EPCInnerProps> = ({
  cabinetId,
  permissions,
  isFullSize,
  height,
  isFullFilesAccess,
}) => {
  const [popup, togglePopup] = usePopupsToggle(popupsInitial);

  // Поиск по дереву и навигация по ярлыкам
  const search = WorkGroupEPCStore.hooks.useTreeSearch({
    cabinetId,
    popup,
    togglePopup,
  });

  const epcPermissions = WorkGroupEPCStore.hooks.useEPCPermissions({
    permissions,
    isFullFilesAccess,
  });

  // Параметры для ленивой загрузки дерева
  const lazyTreeParams = {
    treeKey: EPC_TREE_KEY,
    endpoints: ENDPOINTS,
    queryParams: {
      cabinetId,
      user: 'admin',
      isCabinet: true,
    },
    transformResponse: WorkGroupEPCConfig.transformResponse,
  };

  const {
    treeData,
    loadData,
    statuses,
    resetTree,
    refetchNode,
    treeActions,
    abortRequests,
  } = newlazyTree.hooks.useTreeData(lazyTreeParams);

  const treeKeysProps = newlazyTree.hooks.useTreeKeys(EPC_TREE_KEY);

  const [fileVisibiltiyStatuses, saveStatus] =
    WorkGroupEPCStore.hooks.useVisibilityFileChange();

  // Инициализация кнопок управления
  const buttons = WorkGroupEPCStore.hooks.useButtons({
    actionOnCheckedNodes: treeActions.actionOnCheckedNodes,
    resetTree,
    treeKey: EPC_TREE_KEY,
    togglePopup,
    epcPermissions,
  });

  // Измерение высоты панели кнопок для корректного отображения дерева
  const [refButtons, { height: buttonsHeight }] = useMeasure<HTMLDivElement>();
  const treeHeight = height - buttonsHeight;

  // Настройка drag-n-drop функционала
  const [draggable, isDropForbidden, treeDragProps] =
    WorkGroupEPCStore.hooks.useTreeDragNDrop({
      epcPermissions,
      moveNode: treeActions.moveNode,
      isDraggable: true,
    });

  // Модифицированный селект (выделять можно только директории)
  const handleSelect = WorkGroupEPCStore.hooks.useTreeSelect(
    treeKeysProps.onSelect,
  );

  // Настройка отображения заголовков узлов дерева
  const titleRender = WorkGroupEPCStore.hooks.useTreeTitle({
    deleteNode: treeActions.deleteNode,
    refetchNode,
    saveStatus,
    findedItemId: search.foundItemId,
    togglePopup,
    epcPermissions,
  });

  return (
    <div className={styles.epcInner} style={{ height }}>
      <ApiContainer
        error={statuses.error as AppError}
        isPending={statuses.isLoading}
        className={classNames(
          styles.popupApi,
          isFullSize && styles.popupApiFull,
        )}
      >
        <Tree
          treeData={treeData}
          loadData={loadData}
          showLine
          selectable
          checkStrictly
          ref={search.treeRef}
          height={treeHeight}
          checkable={
            epcPermissions.canCopyFiles ||
            epcPermissions.canDownloadFiles ||
            epcPermissions.canEdit
          }
          className={classNames(styles.tree, isFullSize && styles.treeFull, {
            [styles.dropForbidden]: isDropForbidden,
          })}
          draggable={draggable}
          titleRender={titleRender}
          {...treeDragProps}
          {...treeKeysProps}
          onSelect={handleSelect}
        />
      </ApiContainer>

      <div className={styles.buttons} ref={refButtons}>
        <ButtonsContainer buttons={buttons} disableAnimation />
        <TreeSearch
          isLoading={statuses.isLoading || statuses.loadingBranch}
          togglePopup={() => togglePopup('search')}
          treeSearchConfig={{
            arrowDown: search.arrowDown,
            arrowUp: search.arrowUp,
            currentIndex: search.currentIndex,
            taggedNodes: search.taggedNodes,
          }}
        />
      </div>

      {/* Модальные окна */}
      <SearchDrawer
        placement="right"
        width={550}
        handleClose={() => {
          search.abortSearch();
          togglePopup('search');
        }}
        isOpened={popup.search}
        handleResetSearch={search.handleResetSearchTags}
        formFields={search.formFields}
        values={search.formValues}
        setValues={search.setFormValues}
        searchCallback={search.handleSearch}
      />

      {popup.watch && (
        <WatchModal
          treeRef={search.treeRef}
          cabinetId={cabinetId}
          epcPermissions={epcPermissions}
          togglePopup={togglePopup}
        />
      )}

      {popup.edit && (
        <EditModal
          loadData={loadData}
          handleClose={() => {
            search.closeEditModal();
            abortRequests();
          }}
        />
      )}

      <EPCDownloadModal
        cabinetId={cabinetId}
        isOpened={popup.downloadEPC}
        handleClose={() => togglePopup('downloadEPC')}
      />

      <VisibilityFilesModal
        actionOnCheckedNodes={treeActions.actionOnCheckedNodes}
        isVisibilityPending={fileVisibiltiyStatuses.isPending}
        handleSave={saveStatus}
        isOpened={popup.visibility}
        handleClose={() => togglePopup('visibility')}
      />

      <CopyModalWithTree
        loadData={loadData}
        epcPermissions={epcPermissions}
        refetchNode={refetchNode}
        handleClose={() => togglePopup('copy')}
        isOpened={popup.copy}
        cabinetId={cabinetId}
      />
    </div>
  );
};

export const WorkGroupEPC: FC<WorkGroupEPCProps> = ({
  handleClose,
  isOpened,
  ...props
}) => {
  const [isFullSize, setIsFullSize] = useState(false);

  return (
    <AppPopup
      fullSizeClassName={styles.popupFull}
      additionalFullSizeHandler={setIsFullSize}
      isOpened={isOpened}
      onClose={handleClose}
      className={styles.popup}
      title="ЭПП"
    >
      <AutoSizer disableWidth className={styles.autosizer}>
        {({ height }) => (
          <EPCInner {...props} isFullSize={isFullSize} height={height} />
        )}
      </AutoSizer>
    </AppPopup>
  );
};
