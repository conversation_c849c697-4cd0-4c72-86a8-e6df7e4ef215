import { BulbTwoTone } from '@ant-design/icons';
import { Divider, notification, Result, Tree } from 'antd';
import { FC, memo, useCallback, useEffect, useMemo, useState } from 'react';
import {
  WorkGroupEPCStore,
  WorkGroupEPCLib,
  WorkGroupEPCConfig,
} from 'widgets/WorkGroupEPC';
import { LoadData, newlazyTree } from 'features/NewLazyTree';
import { apiUrls } from 'shared/api';
import { getFilteredFlatTreeByParam } from 'shared/lib';
import {
  renderTreeTitle,
  useAppDispatch,
  useAppSelector,
  useCreateSliceActions,
} from 'shared/model';
import { AppPopup } from 'shared/ui';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import { CardsLink } from '../CardsLink';
import styles from './styles.module.scss';

type EditModalProps = {
  handleClose: Callback;
  loadData: LoadData;
};

export const EditModal: FC<EditModalProps> = memo(
  ({ handleClose, loadData }) => {
    const { EPC_TREE_KEY } = WorkGroupEPCConfig;
    const dispatch = useAppDispatch();
    const { getBodyLinks } = WorkGroupEPCLib;
    const loadedKeys = useAppSelector((state) =>
      newlazyTree.selectors.loadedKeysSelector(state, {
        treeKey: EPC_TREE_KEY,
      }),
    );

    const { handleUpdateLinked, handleResetLinked } = useCreateSliceActions(
      WorkGroupEPCStore.reducers.slice.actions,
    );

    const { setExpandedKeys } = useCreateSliceActions(
      newlazyTree.reducers.slice.actions,
    );

    const loadingKeys = useAppSelector((state) =>
      newlazyTree.selectors.loadingKeysSelector(state, {
        treeKey: EPC_TREE_KEY,
      }),
    );

    const loadedKeysFromMainTree = useAppSelector((state) =>
      newlazyTree.selectors.loadedKeysSelector(state, {
        treeKey: EPC_TREE_KEY,
      }),
    );

    const [expandedKeys, setExpandedKeysState] = useState<Key[]>(
      () => loadedKeysFromMainTree,
    );

    const handleExpand = useCallback((keys: Key[]) => {
      setExpandedKeysState(keys);
    }, []);

    useEffect(() => {
      setExpandedKeysState(loadedKeysFromMainTree);
    }, [loadedKeysFromMainTree]);

    useEffect(() => {
      // Убираем загружаемые сейчас ноды из раскрытых в основном дереве
      // Нужно для того, чтобы два дерева не начали отменять повторные запросы друг друга
      setExpandedKeys({
        isExpanded: false,
        nodeIds: loadingKeys as string[],
        treeKey: WorkGroupEPCConfig.EPC_TREE_KEY,
      });
    }, []); // eslint-disable-line

    const { linkedData } = useAppSelector(
      WorkGroupEPCStore.selectors.epcSelector,
    );
    const tree = useAppSelector((state) =>
      newlazyTree.selectors.treeSelector(state, {
        treeKey: EPC_TREE_KEY,
      }),
    );
    const { updateNodes } = useCreateSliceActions(
      newlazyTree.reducers.slice.actions,
    );

    const handleCloseModal = useCallback(() => {
      handleClose();
      handleResetLinked();
    }, []); // eslint-disable-line

    const idKeyMap = useMemo(
      () =>
        getFilteredFlatTreeByParam(tree, 'key').reduce((acc, node) => {
          acc[node.itemId!] = String(node.key!);
          return acc;
        }, {} as Record<string, string>),
      [tree],
    );

    const checkedKeys = useMemo<Key[]>(
      () => linkedData.editLinked.map((item) => idKeyMap[item.itemId!]),
      [linkedData.editLinked, idKeyMap],
    );

    const parsedTree = useMemo(
      () =>
        WorkGroupEPCLib.modifyAndFilterTreeByConfigs(
          tree,
          () => true,
          (node) =>
            ({
              ...node,
              disableCheckbox: node.itemId === linkedData.directoryId,
            } as TreeElement),
        ),
      [tree, linkedData.directoryId],
    );

    const onCheck = useCallback(
      (_, node: { checked: boolean; node: TreeElement }) => {
        const editLinked = node.checked
          ? [
              ...linkedData.editLinked,
              {
                itemId: node.node.itemId,
                title: node.node.title,
                isDirectory: node.node.isDirectory,
              },
            ]
          : linkedData.editLinked.filter((i) => i.itemId !== node.node.itemId);

        handleUpdateLinked({
          ...linkedData,
          editLinked,
        });
      },
      [linkedData, handleUpdateLinked],
    );

    const [isPending, setIsPending] = useState(false);

    return (
      <AppPopup
        closeOnEscape={!isPending}
        isCloseButtonDisabled={isPending}
        shouldCloseOnBackdropClick={!isPending}
        fullSizeClassName={styles.popupFull}
        isOpened
        className={styles.popup}
        onClose={handleCloseModal}
        title={`Редактирование списка ярлыков для "${linkedData.title}"`}
      >
        <div className={styles.transfer}>
          <div className={styles.transferTree}>
            <Tree
              checkable
              selectable={false}
              loadedKeys={loadedKeys}
              checkStrictly
              checkedKeys={checkedKeys}
              onCheck={onCheck}
              treeData={parsedTree}
              expandedKeys={expandedKeys}
              loadData={loadData}
              onExpand={handleExpand}
              height={400}
              showLine
              titleRender={renderTreeTitle}
            />
          </div>

          <Divider type="vertical" className={styles.divider} />

          <div className={styles.transferList}>
            {linkedData.editLinked.length !== 0 ? (
              <CardsLink linked={linkedData.editLinked} isDeletable />
            ) : (
              <Result
                className={styles.empty}
                title="Выберите каталоги и файлы в дереве"
                icon={<BulbTwoTone twoToneColor="#faad14" />}
              />
            )}
          </div>
        </div>

        <ButtonsContainer
          buttons={[
            {
              loading: isPending,
              title: 'Сохранить',
              key: 'save',
              type: 'primary',
              onClick: async () => {
                setIsPending(true);
                const linked = getBodyLinks(linkedData.editLinked);
                const hasLinked =
                  linked.files.length > 0 || linked.catalogs.length > 0;

                await dispatch(
                  WorkGroupEPCStore.actions.postLinkedElementsThunk({
                    url: apiUrls.workGroup.EPC.saveLinkedElements,
                    body: {
                      directoryId: linkedData.directoryId,
                      linkedFiles: linked.files,
                      linkedDirectories: linked.catalogs,
                    },
                  }),
                )
                  .unwrap()
                  .then((data) => {
                    if (data.success) {
                      notification.success({
                        message: 'Связи успешно обновлены',
                      });
                      updateNodes({
                        treeKey: EPC_TREE_KEY,
                        itemIds: [linkedData.directoryId],
                        updateCallback: (node: TreeElement) => {
                          node.hasLinked = hasLinked;
                        },
                      });

                      handleUpdateLinked({
                        ...linkedData,
                        watchLinked: linkedData.editLinked,
                      });
                    } else {
                      notification.error({
                        message: `Ошибка обновления связей`,
                      });
                    }

                    handleClose();
                  })
                  .finally(() => {
                    setIsPending(false);
                  });
              },
            },
            {
              loading: isPending,
              title: 'Отмена',
              key: 'cancel',
              danger: true,
              ghost: true,
              onClick: handleCloseModal,
            },
          ]}
        />
      </AppPopup>
    );
  },
);
