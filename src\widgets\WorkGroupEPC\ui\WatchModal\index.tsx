import { notification } from 'antd';
import { FC, useState } from 'react';
import {
  EpcPermissions,
  WorkGroupEPCConfig,
  WorkGroupEPCStore,
} from 'widgets/WorkGroupEPC';
import { newlazyTree } from 'features/NewLazyTree';
import { permissionsConfig } from 'entities/Permissions';
import { useAppSelector } from 'shared/model';
import { AppPopup } from 'shared/ui';
import { ButtonsContainer } from 'shared/ui/ButtonsContainer';
import { CardsLink } from '../CardsLink';

import styles from './styles.module.scss';

interface WatchModalProps {
  cabinetId: string;
  epcPermissions: EpcPermissions;
  togglePopup: (name: keyof typeof WorkGroupEPCConfig.popupsInitial) => void;
  treeRef: React.MutableRefObject<null>;
}

export const WatchModal: FC<WatchModalProps> = ({
  togglePopup,
  epcPermissions,
  treeRef,
  cabinetId,
}) => {
  const epc = useAppSelector(WorkGroupEPCStore.selectors.epcSelector);
  const [isPending, setIsPending] = useState(false);

  const entities = useAppSelector((state) =>
    newlazyTree.selectors.entitiesSelector(state, {
      treeKey: WorkGroupEPCConfig.EPC_TREE_KEY,
    }),
  );

  const [handleLinkClick, abortRequest] = WorkGroupEPCStore.hooks.useTreeLink(
    entities,
    treeRef,
    cabinetId,
    () => togglePopup('watch'),
    setIsPending,
  );

  const tooltipText =
    (!epcPermissions.canEdit
      ? permissionsConfig.warnMessages.noPermissionsDefault('редактирование')
      : undefined) ||
    (isPending ? 'Выполняется переход к cвязанному элементу' : undefined);

  return (
    <AppPopup
      isOpened
      className={styles.popup}
      onClose={() => {
        togglePopup('watch');
        abortRequest();
        if (isPending) {
          notification.warn({
            message: 'Переход к связанному элементу отменен',
          });
        }
      }}
      title={`Список ярлыков для "${epc.linkedData.title}"`}
    >
      <CardsLink
        setIsPending={setIsPending}
        linked={epc.linkedData.watchLinked}
        handleCardClick={handleLinkClick}
        hoverable
      />

      <ButtonsContainer
        buttons={[
          {
            title: 'Редактировать привязки',
            disabled: !epcPermissions.canEdit,
            loading: isPending,
            tooltip: tooltipText,
            key: 'edit',
            onClick: () => togglePopup('edit'),
          },
        ]}
      />
    </AppPopup>
  );
};
