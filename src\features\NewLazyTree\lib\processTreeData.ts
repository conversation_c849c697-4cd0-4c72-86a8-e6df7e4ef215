import { TreeState } from '../types';
import { updateTotalCountOfLeafs } from './updateTotalCountOfLeafs';

export const processTreeData = (
  treeData: TreeElement[],
  tree: TreeState,
  parentId?: string,
  appendMode = false,
): void => {
  const { entities } = tree;

  treeData.forEach((node) => {
    node.disabled = false;
    node.pos = node.key as string;
    node.key = node.itemId!;
    node.isDisabled = node.disabled;

    // Проверяем, есть ли дети в загруженной ноде, если нет проверяем есть ли в стейте
    const childrenIds =
      node.children && node.children.length > 0
        ? node.children
            .filter((child) => !!child.itemId)
            .map((child) => child.itemId!)
        : entities[node.itemId!]?.childrenIds || [];

    // Записываем ноду в стейт
    entities[node.itemId!] = {
      ...node,
      childrenIds,
    };

    // Если у ноды нет родителя, добавляем её в массив корневых нод
    if (!parentId && !tree.rootId.includes(node.itemId!)) {
      tree.rootId.push(node.itemId!);
    }

    const isParentNodeChecked = parentId && tree.checkedKeys[parentId];
    if (isParentNodeChecked && node.itemId) {
      tree.checkedKeys[node.itemId] = true;
    }

    if (node.children && node.children.length > 0 && node.itemId) {
      processTreeData(node.children, tree, node.itemId);
    }
  });

  const parentNode = parentId && entities[parentId];

  if (parentNode) {
    const newChildrenIdsSet = new Set(
      treeData.map((node) => node.itemId as string),
    );

    // Переносим информацию о пагинации от первого дочернего элемента к родительскому
    if (treeData.length > 0 && treeData[0].total !== undefined) {
      parentNode.total = treeData[0].total;
      parentNode.page = treeData[0].page;
      parentNode.size = treeData[0].size;
    }

    if (appendMode) {
      // В режиме append добавляем новые элементы к существующим
      const existingChildrenIds = parentNode.childrenIds || [];
      const allChildrenIds = [...existingChildrenIds, ...newChildrenIdsSet];

      // Удаляем дубликаты
      parentNode.childrenIds = [...new Set(allChildrenIds)];
      parentNode.childrenCount = parentNode.childrenIds.length;
      parentNode.isLeaf = parentNode.childrenIds.length === 0;
    } else {
      // В обычном режиме заменяем все дочерние элементы
      const deletedChildrenIds = parentNode.childrenIds?.filter(
        (id) => !newChildrenIdsSet.has(id),
      );

      deletedChildrenIds?.forEach((itemId) => {
        delete tree.checkedKeys[itemId];
        delete tree.entities[itemId];
        delete tree.loadedKeys[itemId];
        delete tree.expandedKeys[itemId];
      });

      parentNode.childrenIds = [...newChildrenIdsSet];
      parentNode.childrenCount = treeData.length;
      parentNode.isLeaf = treeData.length === 0;
    }

    if (parentNode.childrenIds && parentNode.childrenIds.length === 0) {
      delete tree.expandedKeys[parentId];
      delete tree.loadedKeys[parentId];
    } else {
      tree.loadedKeys[parentId] = true;
      tree.expandedKeys[parentId] = true;
    }

    updateTotalCountOfLeafs(parentNode.itemId, tree);
  }
};
