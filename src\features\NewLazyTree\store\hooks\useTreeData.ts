import { AxiosResponse } from 'axios';
import { useCallback, useEffect, useRef } from 'react';
import { Endpoints, LoadData } from 'features/NewLazyTree/types';
import { useAppDispatch, useAppSelector } from 'shared/model';
import { slice } from '../reducer';
import { treeSelector, treeStatusesSelector } from '../selectors';
import { getTreeThunk, loadMoreCatalogItemsThunk } from '../thunks';
import { useLoadQueueParralell } from './useLoadQueue';
import { useTreeActions } from './useTreeActions';

export const useTreeData = ({
  treeKey,
  endpoints,
  queryParams,
  transformResponse,
  initialData,
}: {
  endpoints: Endpoints;
  queryParams: Record<string, unknown>;
  treeKey: string;
  initialData?: TreeElement[];
  transformResponse?: (
    response: AxiosResponse,
    parentId?: string,
  ) => TreeElement[];
}): {
  abortRequests: Callback;
  loadData: LoadData;
  loadMoreCatalogItems: (parentId: string) => Promise<void>;
  refetchNode: (itemId: string) => Promise<void>;
  resetTree: () => Promise<void>;
  statuses: typeof statuses;
  treeActions: ReturnType<typeof useTreeActions>;
  treeData: TreeElement[];
} => {
  const dispatch = useAppDispatch();
  const treeData = useAppSelector((state) => treeSelector(state, { treeKey }));
  const statuses = useAppSelector((state) =>
    treeStatusesSelector(state, { treeKey }),
  );

  const controllersRef = useRef<Map<string, AbortController>>(new Map());

  const getAbortController = useCallback((key: string): AbortController => {
    let controller = controllersRef.current.get(key);
    if (controller) {
      controller.abort();
    }

    controller = new AbortController();
    controllersRef.current.set(key, controller);

    return controller;
  }, []);

  const handleLoadData = useCallback<LoadData>(
    ({ itemId, isLeaf }) =>
      new Promise<void>((resolve, reject) => {
        if (isLeaf) {
          resolve();
        } else {
          const controller = getAbortController(itemId!);
          dispatch(
            getTreeThunk({
              itemId,
              treeKey,
              endpoint: endpoints.fetch,
              queryParams,
              transformResponse,
              signal: controller.signal,
            }),
          )
            .unwrap()
            .then(() => {
              controllersRef.current.delete(itemId!);
              resolve();
            })
            .catch((err) => {
              controllersRef.current.delete(itemId!);
              if (err === 'Canceled') {
                // Если ошибка вызвана абортом
                resolve();
              } else {
                reject(err);
              }
            });
        }
      }),
    [
      dispatch,
      endpoints.fetch,
      getAbortController,
      queryParams,
      transformResponse,
      treeKey,
    ],
  );

  const loadData = useLoadQueueParralell(handleLoadData);

  const abortRequests = useCallback(() => {
    controllersRef.current.forEach((controller) => {
      if (!controller.signal.aborted) {
        controller.abort();
      }
    });
    controllersRef.current.clear();
  }, []);

  useEffect(() => {
    if (initialData) {
      dispatch(
        slice.actions.initializeTreeWithData({
          treeKey,
          treeData: initialData,
        }),
      );
    } else if (treeData.length === 0) {
      const controller = getAbortController('root');
      dispatch(
        getTreeThunk({
          queryParams,
          endpoint: endpoints.fetch,
          treeKey,
          transformResponse,
          signal: controller.signal,
        }),
      );
    }

    return () => {
      abortRequests();
      dispatch(slice.actions.reset(treeKey));
    };
    // eslint-disable-next-line
  }, [treeKey]);

  const resetRef = useRef<AbortController | null>(null);

  const resetTree = useCallback(async (): Promise<void> => {
    dispatch(slice.actions.reset(treeKey));
    abortRequests();

    if (resetRef.current) {
      resetRef.current.abort();
    }

    resetRef.current = new AbortController();

    await dispatch(
      getTreeThunk({
        queryParams,
        endpoint: endpoints.fetch,
        treeKey,
        transformResponse,
        signal: resetRef.current.signal,
      }),
    ).unwrap();
  }, [
    abortRequests,
    dispatch,
    endpoints.fetch,
    queryParams,
    transformResponse,
    treeKey,
  ]);

  const refetchNode = useCallback(
    (itemId: string): Promise<void> =>
      new Promise((resolve, reject) => {
        const controller = getAbortController(itemId!);
        dispatch(
          getTreeThunk({
            endpoint: endpoints.fetch,
            itemId,
            treeKey,
            queryParams,
            isRefetch: true,
            transformResponse,
            signal: controller.signal,
          }),
        )
          .then(() => resolve())
          .catch((error) => reject(error));
      }),
    [
      dispatch,
      endpoints.fetch,
      getAbortController,
      queryParams,
      transformResponse,
      treeKey,
    ],
  );

  const loadMoreCatalogItems = useCallback(
    (parentId: string): Promise<void> =>
      new Promise((resolve, reject) => {
        const controller = getAbortController(`load-more-${parentId}`);
        dispatch(
          loadMoreCatalogItemsThunk({
            endpoint: endpoints.fetch,
            parentId,
            treeKey,
            queryParams,
            transformResponse,
            signal: controller.signal,
          }),
        )
          .unwrap()
          .then(() => {
            controllersRef.current.delete(`load-more-${parentId}`);
            resolve();
          })
          .catch((err) => {
            controllersRef.current.delete(`load-more-${parentId}`);
            if (err === 'Canceled') {
              resolve();
            } else {
              reject(err);
            }
          });
      }),
    [
      dispatch,
      endpoints.fetch,
      getAbortController,
      queryParams,
      transformResponse,
      treeKey,
    ],
  );

  const treeActions = useTreeActions({
    treeKey,
    endpoints,
    queryParams,
    transformResponse,
    controllersRef,
    getAbortController,
  });

  return {
    treeData,
    statuses,
    loadData,
    loadMoreCatalogItems,
    resetTree,
    refetchNode,
    treeActions,
    abortRequests,
  };
};
