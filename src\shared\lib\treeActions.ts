import { notification, TreeNodeProps, TreeProps } from 'antd';
import { EventDataNode } from 'antd/lib/tree';
import { createConfirmModal } from 'shared/model';
import { getTreeElementByIndexes } from './getElementByIndexes';
import { getTreeIndexMap } from './getTreeIndexMap';
import { throttle } from './throttle';

const WARN_TEXTS = {
  SYSTEM: 'Нельзя переносить файлы в системные директории.',
  SELF: 'Нельзя перенести файл в директорию, в которой он уже расположен.',
  DUPLICATE: (name: string) =>
    `Файл ${name} уже имеется в папке назначения и не может быть перемещен`,
};

type HandleDND<T = MoveDNDBody, R = string> = (
  treeAddition: (tree: TreeElement[]) => void,
  oldTree: TreeElement[],
  moveCallBack: (body: T) => Promise<R>,
  root?: TreeElement,
) => TreeProps<TreeElement>['onDrop'];

type HandleLazyDND<T = MoveDNDBody, R = string> = (
  entities: Record<string, TreeElement | undefined>,
  moveCallBack: (body: T, node: TreeElement) => Promise<R>,
) => TreeProps<TreeElement>['onDrop'];

const loop = (
  dataTree: TreeElement[],
  key: Key,
  callback: (
    node: TreeElement,
    i: number,
    dataTree: TreeElement[],
    parent?: TreeElement,
  ) => void,
  parent?: TreeElement,
): void => {
  dataTree.forEach((item, index, arr) =>
    item.key === key
      ? callback(item, index, arr, parent)
      : item.children
      ? loop(item.children, key, callback, item)
      : item,
  );
};

/**
 * Создает глубокую копию дерева, в котором каждый узел является объектом с
 * массивом потомков
 *
 * @param {TreeElement[]} tree - Исходное дерево, которое нужно скопировать
 * @returns {TreeElement[]} - Глубокая копия исходного дерева
 */
export const deepCopyTree = (tree: TreeElement[]): TreeElement[] =>
  tree.map((item) => ({
    ...item,
    ...(item.children && { children: deepCopyTree(item.children) }),
  }));

/**
 * Создает обработчик события `onDrop` для перемещения элементов в ленивой
 * древовидной структуре.
 *
 * Функция обрабатывает перетаскивание элементов в дереве, включая:
 *
 * - Определение перетаскиваемого и целевого узлов
 * - Вызов moveCallBack для выполнения серверной операции перемещения
 * - Обработку ошибок и отображение предупреждений
 * - Обновление состояния затронутых узлов через refetchNode
 * - Учет различных сценариев перетаскивания (между уровнями, внутри потомков)
 *
 * @type {HandleLazyDND}
 * @param oldTree - Исходное состояние дерева.
 * @param moveCallBack - Функция обратного вызова для выполнения операции
 *   перемещения на сервере. Принимает объект с параметрами {itemId, parentId,
 *   position} и возвращает строку (пустую в случае успеха или сообщение об
 *   ошибке).
 * @param refetchNode - Функция для обновления состояния узла после перемещения.
 * @returns Function Асинхронный обработчик события `onDrop` для компонента
 *   `Tree`.
 */

export const handleLazyTreeDnD: HandleLazyDND =
  (entities, moveCallBack): TreeProps<TreeElement>['onDrop'] =>
  async (info): Promise<void> => {
    const dragNode = info.dragNode as EventDataNode<TreeElement>;
    const dropNode = info.node as EventDataNode<TreeElement>;
    const dropParentNode = entities[dropNode.parent!];
    const targetNode = info.dropToGap ? dropParentNode || dropNode : dropNode;

    if (
      // Если бросаем непосредственно системную директорию
      !info.dropToGap &&
      dropNode.isFixed === 2
    ) {
      notification.warn({
        message: WARN_TEXTS.SYSTEM,
      });
      return;
    }

    // Если цель та же директория, где файл расположен
    // То есть бросаем в gap между элементами в той же директории, где расположен файл
    if (dragNode.parent === dropNode.parent && info.dropToGap) {
      notification.warn({
        message: WARN_TEXTS.SELF,
      });
      return;
    }

    // Если непосредственный родитель
    if (dragNode.parent === dropNode.itemId) {
      notification.warn({
        message: WARN_TEXTS.SELF,
      });
      return;
    }

    // Если родитель системная директория
    if (info.dropToGap && dropParentNode?.isFixed === 2) {
      notification.warn({
        message: WARN_TEXTS.SYSTEM,
      });
      return;
    }

    const hasDuplicate = targetNode?.childrenIds
      ?.map((id) => entities[id]?.title)
      .some((title) => title && title === dragNode.title);

    if (hasDuplicate) {
      notification.warn({
        message: WARN_TEXTS.DUPLICATE(dragNode.title as string),
      });
      return;
    }

    createConfirmModal({
      title: 'Перенос файла',
      onConfirm: async () => {
        const res = await moveCallBack(
          {
            itemId: dragNode.itemId!,
            parentId: targetNode.itemId!,
            position: info.dropPosition,
          },
          dragNode,
        );

        if (res !== '') {
          notification.warn({ message: res });
        }
      },
      message: `Вы действительно хотите переместить файл "${dragNode.title}" в каталог "${targetNode.title}"?`,
    });
  };

/**
 * Обработчик события `onDrop` для перемещения элементов в древовидной структуре
 * жадного дерева.
 *
 * @type {HandleDND}
 * @param treeAddition - Функция, которая будет вызвана после перемещения
 *   элемента и принимает обновленное древо.
 * @param oldTree - Исходное древо, которое необходимо обновить.
 * @param moveCallBack - Функция обратного вызова для отправки запроса на сервер
 *   при перемещении элемента.
 * @param root - Рут дерева.
 * @returns - Обработчик события `onDrop` для компонента `Tree`.
 */

export const handleTreeDnD: HandleDND =
  (
    treeAddition,
    oldTree,
    moveCallBack,
    root,
  ): TreeProps<TreeElement>['onDrop'] =>
  async (info): Promise<void> => {
    const dropKey = info.node.key;
    const dragKey = info.dragNode.key;
    const dropPos = info.node.pos.split('-');
    const dropPosition =
      info.dropPosition - Number(dropPos[dropPos.length - 1]);
    let dragObj: TreeElement;

    const newTree = deepCopyTree(oldTree);

    if (dragKey && dropPosition !== -1) {
      loop(newTree, dragKey, (item, index, arr) => {
        arr.splice(index, 1);
        dragObj = item;
      });
    }
    if (!info.dropToGap && dropKey) {
      loop(newTree, dropKey, (item) => {
        if (item.isDirectory) {
          item.isLeaf = false;
          item.children = item.children || [];
          item.children.unshift(dragObj);
        }
      });
    } else if (
      ((info.node as TreeNodeProps).props.children || []).length > 0 &&
      (info.node as TreeNodeProps).props.expanded &&
      dropPosition === 1
    ) {
      loop(newTree, dropKey, (item) => {
        item.children = item.children || [];
        item.children.unshift(dragObj);
      });
    } else if (dropKey) {
      let ar: TreeElement[] = [];
      let i: number;

      loop(newTree, dropKey, (_, index, arr) => {
        ar = arr;
        i = index;
      });

      if (dropPosition !== -1) {
        ar.splice(i! + 1, 0, dragObj!);
      }
    }

    const indexMap = getTreeIndexMap(newTree, 'itemId');
    treeAddition(deepCopyTree(newTree));
    const indexes = indexMap.get(dragObj!.itemId as string);

    if (indexes && indexes?.length > 1) {
      const lastIndex = indexes.at(-1) as number;
      const anotherIndexes = indexes.slice(0, -1) as NonEmptyArray<number>;

      const foundNode = getTreeElementByIndexes(newTree, anotherIndexes);

      if (moveCallBack && (info.dropToGap || dropKey) && foundNode) {
        const res = await moveCallBack({
          itemId: foundNode?.children?.[lastIndex]?.itemId || '',
          parentId: foundNode?.itemId || root?.itemId || '',
          position: lastIndex + 1,
        });

        if (res !== '') {
          notification.warn({ message: res });
          treeAddition(deepCopyTree(oldTree));
        } else {
          treeAddition(deepCopyTree(newTree));
        }
      }
    }
  };

const [scrollTo] = throttle((target: HTMLElement) => {
  const { bottom: currentBottom, top: currentTop } =
    target.getBoundingClientRect();
  const treeHolder = document.getElementsByClassName('ant-tree-list-holder')[0];
  const { bottom: boxBottom, top: boxTop } = treeHolder.getBoundingClientRect();

  const scrollZone = 100; // Зона прокрутки в пикселях
  const scrollStep = 32; // Шаг прокрутки в пикселях

  if (currentTop > boxBottom - scrollZone) {
    treeHolder.scrollTop += scrollStep;
  }

  if (boxTop + scrollZone > currentBottom) {
    treeHolder.scrollTop -= scrollStep;
  }
}, 100);

export type OnDragOver = NonNullable<TreeProps<TreeElement>['onDragOver']>;

export const onDragOver: OnDragOver = (info) => {
  if (info.event.target instanceof HTMLElement) {
    scrollTo(info.event.target);
  }
};
