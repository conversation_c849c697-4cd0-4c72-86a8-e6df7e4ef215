import { notification } from 'antd';
import axios from 'axios';
import { useEffect, useMemo } from 'react';
import { WorkGroupEPCConfig, WorkGroupEPCStore } from 'widgets/WorkGroupEPC';
import { EpcPermissions } from 'widgets/WorkGroupEPC/types';
import { newlazyTree } from 'features/NewLazyTree';
import { permissionsConfig } from 'entities/Permissions';
import { appErrorNotification } from 'shared/lib';
import { createBasicClosableNotice, useCreateSliceActions } from 'shared/model';

const createConflictNotice = (message: string): void =>
  createBasicClosableNotice({
    message: 'Внимание',
    description: message,
  });

export const useCopyModalButtons = (
  handleClose: Callback,
  selectedTree: TreeElement[],
  isDisabled: boolean,
  refetch: Callback,
  epcPermissions: EpcPermissions,
  cabinetId: string,
  itemId: string,
): AdditionalButton[] => {
  const [saveFiles, stateSaveFiles] = WorkGroupEPCStore.hooks.useCopyFile(
    cabinetId,
    itemId,
  );
  const { EPC_TREE_KEY } = WorkGroupEPCConfig;
  const { clearCheckedKeys } = useCreateSliceActions(
    newlazyTree.reducers.slice.actions,
  );
  const resetCheckboxes = (): void =>
    clearCheckedKeys({ treeKey: EPC_TREE_KEY });

  const createHandleSave =
    ({ flat, items }: { flat: boolean; items: Partial<TreeElement>[] }) =>
    async () => {
      try {
        await saveFiles('copyFiles', { items, flat });
      } catch (err) {
        if (axios.isAxiosError(err)) {
          if (err.response?.data?.error) {
            appErrorNotification(err.response?.data?.error, err);
          }
        } else {
          appErrorNotification('Ошибка при копировании', err as AppError);
        }
      } finally {
        resetCheckboxes();
      }
      handleClose();
    };

  const { canEditEPCStructure } = epcPermissions;

  const isHaveNotEmptyDir = useMemo(
    () =>
      selectedTree.some(
        ({ isDirectory, totalCountOfLeafs }) =>
          isDirectory && totalCountOfLeafs && totalCountOfLeafs > 0,
      ),
    [selectedTree],
  );

  const isHaveFiles = useMemo(
    () => selectedTree.some(({ isDirectory }) => !isDirectory),
    [selectedTree],
  );

  useEffect(() => {
    if (stateSaveFiles.data === null) {
      return;
    }

    setTimeout(() => {
      refetch();
    }, WorkGroupEPCConfig.DELAY_FOR_REFETCH);

    if (stateSaveFiles.data.length > 0) {
      createConflictNotice(stateSaveFiles.data);
    } else {
      notification.success({ message: 'Файлы успешно скопированы' });
    }
  }, [stateSaveFiles.data]); // eslint-disable-line

  return [
    {
      title: 'Вставить файлы',
      key: 'saveFiles',
      type: 'primary',
      onClick: createHandleSave({ items: selectedTree, flat: true }),
      disabled: isDisabled || (!isHaveFiles && !isHaveNotEmptyDir),
      loading: stateSaveFiles.isPending,
    },
    {
      title: 'Вставить файлы с каталогами',
      key: 'saveFilesWithCatalogs',
      type: 'primary',
      onClick: createHandleSave({ items: selectedTree, flat: false }),
      disabled: isDisabled || !canEditEPCStructure || !isHaveNotEmptyDir,
      tooltip: !canEditEPCStructure
        ? `${permissionsConfig.warnMessages.noPermissionsDefault(
            'изменение структуры каталогов',
          )}`
        : `${
            !isHaveNotEmptyDir
              ? 'Не выбрано ни одной директории для копирования'
              : ''
          }`,
      loading: stateSaveFiles.isPending,
    },
    {
      title: 'Отмена',
      key: 'cancel',
      ghost: true,
      danger: true,
      onClick: handleClose,
    },
  ];
};
