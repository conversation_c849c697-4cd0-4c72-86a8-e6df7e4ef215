import { Checkbox, Typography } from 'antd';
import { FC, useState } from 'react';
import { FilesVisibility, WorkGroupEPCStore } from 'widgets/WorkGroupEPC';
import { TreeActions } from 'features/NewLazyTree';
import { AppPopup, ButtonsContainer } from 'shared/ui';

import styles from './styles.module.scss';

interface VisibilityFilesModalInnerProps {
  actionOnCheckedNodes: TreeActions['actionOnCheckedNodes'];
  handleClose: Callback;
  handleSave: FilesVisibility;
  isVisibilityPending: boolean;
}

type VisibilityFilesModalProps = VisibilityFilesModalInnerProps & {
  isOpened: boolean;
};

const VisibilityFilesModalInner: FC<VisibilityFilesModalInnerProps> = ({
  actionOnCheckedNodes,
  handleClose,
  handleSave,
  isVisibilityPending,
}) => {
  const [isVisible, setIsVisible] = useState(false);

  const buttons = WorkGroupEPCStore.hooks.useVisibilityButtons(
    actionOnCheckedNodes,
    isVisible,
    handleClose,
    handleSave,
    isVisibilityPending,
  );

  return (
    <div className={styles.content}>
      <div className={styles.action}>
        <Typography.Text>
          Элементы скрыты для Контроля выполнения
        </Typography.Text>
        <Checkbox
          checked={isVisible}
          onChange={(e) => setIsVisible(e.target.checked)}
        />
      </div>

      <ButtonsContainer buttons={buttons} />
    </div>
  );
};

export const VisibilityFilesModal: FC<VisibilityFilesModalProps> = ({
  isOpened,
  handleClose,
  isVisibilityPending,
  ...props
}) => (
  <AppPopup
    className={styles.popup}
    isCloseButtonDisabled={isVisibilityPending}
    shouldCloseOnBackdropClick={!isVisibilityPending}
    closeOnEscape={!isVisibilityPending}
    isOpened={isOpened}
    onClose={handleClose}
    title="Доступность элементов"
  >
    <VisibilityFilesModalInner
      handleClose={handleClose}
      isVisibilityPending={isVisibilityPending}
      {...props}
    />
  </AppPopup>
);
