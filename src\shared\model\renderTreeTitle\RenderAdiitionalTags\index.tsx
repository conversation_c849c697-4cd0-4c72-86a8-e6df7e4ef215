import { QuestionCircleOutlined } from '@ant-design/icons';
import { Tag } from 'antd';
import { memo, useMemo } from 'react';
import styles from './styles.module.scss';

interface RenderQuestionsProps {
  questions: string[];
  systemHint?: string;
}

export const RenderAdditionalTags: React.FC<RenderQuestionsProps> = memo(
  ({ questions, systemHint }) => {
    const sortedQuestions = useMemo(
      () => [...questions].sort((a, b) => a.localeCompare(b)),
      [questions],
    );

    return (
      <div className={styles.additionalTags}>
        {systemHint && <Tag color="success">{systemHint}</Tag>}
        {questions.length > 0 && (
          <>
            <span className={styles.questionsHeader}>Коды вопросов:</span>
            <div className={styles.questions}>
              {sortedQuestions.map((item) => (
                <Tag
                  key={item}
                  color="processing"
                  icon={<QuestionCircleOutlined />}
                >
                  {item}
                </Tag>
              ))}
            </div>
          </>
        )}
      </div>
    );
  },
);

RenderAdditionalTags.displayName = 'RenderAdditionalTags';
