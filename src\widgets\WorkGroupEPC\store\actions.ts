import { DownloadBody } from 'widgets/WorkGroupEPC';
import { appInstanceWithoutService } from 'shared/api';
import { createBasicPostThunk } from 'shared/lib';

export const postSearchLinkedElThunk = createBasicPostThunk<
  { treeData: TreeElement[] },
  object
>('workGroupEpc/postSearchLinkedElThunk');

export const postLinkedElementsThunk = createBasicPostThunk<
  { messages: Array<{ message: string; type: string }>; success: boolean },
  object
>('workGroupEpc/postLinkedElementsThunk');

export const getDownloaderLink = createBasicPostThunk<
  {
    data: string | null;
    messages: Array<{ message: string; type: string }>;
    success: boolean;
  },
  DownloadBody
>('workGroupEpc/getDownloaderLink', appInstanceWithoutService);
