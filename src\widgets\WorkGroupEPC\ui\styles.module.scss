@import 'src/app/styles/mixins';

.popup {
  min-width: 1050px;
  width: 55vw;
  height: 70vh;

  &Full {
    height: 90vh;
  }
}

.autosizer {
  width: 100%;
}

.epcInner {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.buttons {
  display: flex;
  gap: 10px;
  justify-content: space-between;
}

:global(.ant-tree-draggable-icon) {
  min-width: 24px;
}

.tree {
  width: 100%;
  flex-grow: 1;
  overflow: auto;

  @include scrollBar;
  @include customTreeTitleWidth;

  :global(.ant-tree-list-holder) {
    @include scrollBar;
  }

  @include virtualScrollbar;
}

.dropForbidden {
  :global(.ant-tree-drop-indicator) {
    background-color: #ff7d7d !important;
  }

  :global(.ant-tree-drop-indicator::after) {
    border: 2px solid #ff7d7d !important;
  }
}
