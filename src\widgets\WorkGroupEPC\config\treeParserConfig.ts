export const updateMainFilePermissions = (
  children: TreeElement[],
): TreeElement[] => {
  if (!children || children.length === 0) {
    return children;
  }

  const mainFile = children.find((node) => node.isMain);
  const files = children.filter((sibling) => !sibling.isDirectory);
  const allCanView = files.every((sibling) => sibling.permissions?.canView);
  const allCanDownload = files.every(
    (sibling) => sibling.permissions?.canDownload,
  );

  if (mainFile && files.length) {
    return files.length > 0
      ? children.map((node) => {
          if (node.isMain) {
            return {
              ...node,
              permissions: {
                canView: allCanView,
                canDownload: allCanDownload,
              },
            };
          }
          return node;
        })
      : children;
  }

  return children;
};

export const treeParserConfig = (node: TreeElement): TreeElement => {
  const newNode = {
    ...node,
    disabled: false,
    isDisabled: node.disabled,
    selectable: node.isDirectory,
    ...(node.children
      ? { children: updateMainFilePermissions(node.children) }
      : {}),
  } as TreeElement;

  return newNode;
};
