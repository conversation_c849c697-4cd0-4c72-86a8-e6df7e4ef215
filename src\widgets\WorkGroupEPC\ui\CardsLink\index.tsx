import {
  DeleteTwoTone,
  FileOutlined,
  FolderOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { Card, Spin, Typography } from 'antd';
import { useState, type FC } from 'react';
import { EditLinked, WorkGroupEPCStore } from 'widgets/WorkGroupEPC';
import { useAppSelector, useCreateSliceActions } from 'shared/model';
import styles from './styles.module.scss';

interface CardsLinkProps {
  linked: EditLinked;
  handleCardClick?: (isDirectory: boolean, itemId: string) => void;
  hoverable?: boolean;
  isDeletable?: boolean;
  setIsPending?: React.Dispatch<React.SetStateAction<boolean>>;
}
export const CardsLink: FC<CardsLinkProps> = ({
  linked,
  setIsPending,
  handleCardClick,
  isDeletable,
  hoverable,
}) => {
  const { handleUpdateLinked } = useCreateSliceActions(
    WorkGroupEPCStore.reducers.slice.actions,
  );

  const [pendingItemId, setPendingItemId] = useState<string>('');
  const epc = useAppSelector(WorkGroupEPCStore.selectors.epcSelector);

  return (
    <div className={styles.container}>
      {linked.map((item) => (
        <Card
          hoverable={hoverable}
          size="small"
          className={styles.containerElement}
          key={item.itemId}
          onClick={() => {
            handleCardClick?.(item.isDirectory!, item.itemId!);
            setPendingItemId?.(item.itemId!);
            setIsPending?.(true);
          }}
        >
          <div className={styles.containerElementContent}>
            <Typography.Text className={styles.containerElementText}>
              {item.isDirectory ? <FolderOutlined /> : <FileOutlined />}
              {item.title}
            </Typography.Text>

            {isDeletable && (
              <DeleteTwoTone
                twoToneColor="#FF4D4F"
                onClick={() =>
                  handleUpdateLinked({
                    ...epc.linkedData,
                    editLinked: linked.filter((i) => i.itemId !== item.itemId),
                  })
                }
              />
            )}
            {pendingItemId === item.itemId && (
              <Spin
                className={styles.containerElementSpin}
                indicator={<LoadingOutlined spin />}
                size="small"
              />
            )}
          </div>
        </Card>
      ))}
    </div>
  );
};
