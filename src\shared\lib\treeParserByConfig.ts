/**
 * Разбирает дерево узлов типа `TreeElement` в соответствии с заданной
 * конфигурационной функцией.
 *
 * @param tree Исходное дерево для разбора.
 * @param config Конфигурационная функция, применяемая к каждому узлу.
 * @returns Разобранное дерево типа `TreeElement[]`.
 */
export const treeParserByConfig = (
  tree: TreeElement[],
  config: (node: TreeElement) => TreeElement,
): TreeElement[] => {
  if (!tree) {
    return [];
  }
  return tree.map((node) => {
    const newNode = config(node);

    if (node.children && node.children.length) {
      newNode.children = treeParserByConfig(node.children, config);
    }
    return newNode;
  });
};
