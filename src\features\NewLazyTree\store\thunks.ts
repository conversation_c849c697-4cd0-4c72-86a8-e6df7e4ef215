import { notification } from 'antd';
import axios, { AxiosResponse } from 'axios';
import { appInstance } from 'shared/api';
import { createAppAsyncThunk } from 'shared/lib';
import { newlazyTree } from '..';
import { mergeSignals } from '../lib';

// Хранит контроллеры отмены для каждого ключа
const pendingRequests = new Map<string | number, AbortController>();

export const getTreeThunk = createAppAsyncThunk<
  TreeElement[],
  {
    endpoint: string;
    treeKey: string;
    body?: object;
    isRefetch?: boolean;
    isSearch?: boolean;
    itemId?: string;
    queryParams?: Record<string, unknown>;
    signal?: AbortSignal;
    transformResponse?: (
      response: AxiosResponse,
      itemId?: string,
    ) => TreeElement[];
  }
>('newLazyTree/getTreeThunk', async (args, { rejectWithValue }) => {
  // Извлекаем параметры из args
  const {
    endpoint,
    queryParams,
    itemId,
    body,
    signal: customSignal,
    transformResponse,
  } = args;

  const cancelKey = (itemId || 'root') as string | number;

  // Создаем внутренний AbortController для управления отменой запроса
  let internalAbortController: AbortController | undefined;

  if (cancelKey !== undefined) {
    // Отменяем предыдущий запрос с тем же ключом
    const previousController = pendingRequests.get(cancelKey);
    if (previousController) {
      previousController.abort();
      pendingRequests.delete(cancelKey);
    }
    // Создаем новый AbortController и сохраняем его в Map
    internalAbortController = new AbortController();
    pendingRequests.set(cancelKey, internalAbortController);
  }

  // Объединяем пользовательский сигнал и внутренний сигнал отмены
  const signal = mergeSignals([customSignal, internalAbortController?.signal]);

  try {
    const method = body ? 'post' : 'get';

    const response = await appInstance.request({
      url: endpoint,
      method,
      data: body,
      params: { ...queryParams, ...(itemId && { itemId }) },
      signal,
    });

    const transformedResponse = transformResponse
      ? transformResponse(response || [], itemId)
      : ((response.data.treeData || []) as TreeElement[]);

    // Удаляем контроллер из Map
    pendingRequests.delete(cancelKey);

    return transformedResponse;
  } catch (err) {
    if (axios.isCancel(err)) {
      return rejectWithValue('Canceled');
    }

    // Удаляем контроллер из Map
    pendingRequests.delete(cancelKey);

    if (axios.isAxiosError(err)) {
      return rejectWithValue(err);
    }
    throw err;
  }
});

/**
 * Thunk-экшен для удаления узла из дерева и повторного получения данных его
 * родительского узла.
 */
export const deleteNodeThunk = createAppAsyncThunk<
  string,
  {
    endpoints: { delete: (id: string) => string; refetch: string };
    fileNetId: string;
    itemId: string;
    parentId: string;
    queryParams: Record<string, unknown>;
    treeKey: string;
    signal?: AbortSignal;
    transformResponse?: (data: AxiosResponse) => TreeElement[];
  }
>(
  'newLazyTree/deleteNodeThunk',
  async (
    {
      itemId,
      parentId,
      fileNetId,
      treeKey,
      endpoints,
      queryParams,
      transformResponse,
      signal,
    },
    { getState, rejectWithValue, dispatch },
  ) => {
    const tree = getState().newLazyTrees.trees[treeKey];
    if (!tree) {
      return '';
    }

    try {
      if (!fileNetId) {
        throw new Error('Файл не имеет fileNetId, удаление невозможно!');
      }

      // Отправляем запрос на удаление на сервер
      await appInstance.delete(endpoints.delete(fileNetId));

      dispatch(
        newlazyTree.reducers.slice.actions.setCheckedKeys({
          checked: false,
          nodeId: itemId,
          treeKey,
        }),
      );
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }
      throw err;
    }

    // Повторно запрашиваем родительский узел для обновления его данных
    dispatch(
      getTreeThunk({
        endpoint: endpoints.refetch,
        itemId: parentId,
        treeKey,
        isRefetch: true,
        queryParams,
        signal,
        transformResponse,
      }),
    ).unwrap();

    return '';
  },
);

/**
 * Thunk для перемещения узлов в дереве с обновлением состояния и рефетчем
 * затронутых родительских узлов.
 */
export const moveNodesThunk = createAppAsyncThunk<
  string,
  {
    body: MoveDNDBody;
    endpoint: {
      move: string;
      refetch: string;
    };
    nodes: TreeElement[];
    treeKey: string;
    queryParams?: Record<string, unknown>;
    signal?: AbortSignal;
    transformResponse?: (data: AxiosResponse) => TreeElement[];
  }
>(
  'newLazyTree/moveNodesThunk',
  async (
    { nodes, treeKey, endpoint, body, queryParams, transformResponse, signal },
    { getState, rejectWithValue, dispatch },
  ) => {
    // Получаем текущее состояние дерева
    const tree = getState().newLazyTrees.trees[treeKey];

    if (!tree) {
      return '';
    }

    // Получаем уникальные идентификаторы исходных родительских узлов
    const sourceParentIds = Array.from(
      new Set(nodes.map((node) => node.parent).filter(Boolean)),
    ) as string[];

    const destinationParentId = body.parentId!;
    const targetNode = tree.entities[destinationParentId];

    try {
      const res = await appInstance.post(endpoint.move, body);

      // Если ответ вернулся с сообщением в теле - бизнес ошибка
      if (res.data.length > 0) {
        return String(res.data);
      }

      notification.success({
        message: `${nodes.length === 1 ? 'Файл' : 'Файлы'} ${nodes
          .map((node) => `"${node.title}"`)
          .join(' ')} успешно перенесен${
          nodes.length > 1 ? 'ы' : ''
        } в директорию "${targetNode?.title}".`,
      });
    } catch (err) {
      if (axios.isAxiosError(err)) {
        if (err.response && err.response.data && err.response.data.error) {
          err.response.data.isBusinessLogicError = true;
        }
        return rejectWithValue(err);
      }
      throw err;
    }

    const parentIdsToRefetch = [...sourceParentIds, destinationParentId];

    Promise.all(
      parentIdsToRefetch.map((itemId) =>
        dispatch(
          getTreeThunk({
            endpoint: endpoint.refetch,
            itemId,
            isRefetch: true,
            treeKey,
            queryParams,
            signal,
            transformResponse,
          }),
        ).unwrap(),
      ),
    );
    return '';
  },
);

/** Thunk для загрузки дополнительных элементов каталога (пагинация) */
export const loadMoreCatalogItemsThunk = createAppAsyncThunk<
  TreeElement[],
  {
    endpoint: string;
    treeKey: string;
    parentId: string;
    queryParams?: Record<string, unknown>;
    signal?: AbortSignal;
    transformResponse?: (
      response: AxiosResponse,
      itemId?: string,
    ) => TreeElement[];
  }
>(
  'newLazyTree/loadMoreCatalogItemsThunk',
  async (args, { getState, rejectWithValue }) => {
    const {
      endpoint,
      treeKey,
      parentId,
      queryParams,
      signal: customSignal,
      transformResponse,
    } = args;

    const state = getState();
    const tree = state.newLazyTrees.trees[treeKey];

    if (!tree) {
      return rejectWithValue('Tree not found');
    }

    const parentNode = tree.entities[parentId];
    if (!parentNode) {
      return rejectWithValue('Parent node not found');
    }

    // Определяем следующую страницу для загрузки
    const currentPage = parentNode.page || 1;
    const pageSize = parentNode.size || 20;
    const nextPage = currentPage + 1;

    const cancelKey = `load-more-${parentId}`;

    // Создаем AbortController для управления отменой запроса
    let internalAbortController: AbortController | undefined;

    if (cancelKey !== undefined) {
      const previousController = pendingRequests.get(cancelKey);
      if (previousController) {
        previousController.abort();
        pendingRequests.delete(cancelKey);
      }
      internalAbortController = new AbortController();
      pendingRequests.set(cancelKey, internalAbortController);
    }

    const signal = mergeSignals([
      customSignal,
      internalAbortController?.signal,
    ]);

    try {
      const response = await appInstance.request({
        url: endpoint,
        method: 'get',
        params: {
          ...queryParams,
          itemId: parentId,
          page: nextPage,
          size: pageSize,
        },
        signal,
      });

      const transformedResponse = transformResponse
        ? transformResponse(response || [], parentId)
        : ((response.data.treeData || []) as TreeElement[]);

      pendingRequests.delete(cancelKey);

      return transformedResponse;
    } catch (err) {
      if (axios.isCancel(err)) {
        return rejectWithValue('Canceled');
      }

      pendingRequests.delete(cancelKey);

      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }
      throw err;
    }
  },
);

export const actionOnCheckedNodesThunk = createAppAsyncThunk<
  string,
  {
    actionCallback: (checkedNodes: TreeElement[]) => Promise<void>;
    endpoint: string;
    filterCallback: (node: TreeElement) => boolean;
    successMessage: string;
    treeKey: string;
    queryParams?: Record<string, unknown>;
    signal?: AbortSignal;
    transformResponse?: (data: AxiosResponse) => TreeElement[];
    updateCallback?: (filteredNodes: TreeElement) => void;
  }
>(
  'newLazyTree/actionOnCheckedNodesThunk',
  async (
    {
      treeKey,
      actionCallback,
      filterCallback,
      successMessage,
      endpoint,
      queryParams,
      transformResponse,
      updateCallback,
      signal,
    },
    { getState, dispatch, rejectWithValue },
  ) => {
    const state = getState();
    const tree = state.newLazyTrees.trees[treeKey];

    if (!tree) {
      return '';
    }

    // Получаем проверенные (checked) узлы
    const checkedNodeIds = Object.keys(tree.checkedKeys);
    const checkedNodes = checkedNodeIds.map(
      (id) => tree.entities[id] as TreeElement,
    );
    const filteredCheckedNodes = checkedNodes.filter(filterCallback);

    if (filteredCheckedNodes.length === 0) {
      notification.warning({
        message:
          'Операция не применима к выбранным элементам, или не приведёт к изменению их текущего состояния',
      });
      return '';
    }

    try {
      await actionCallback(filteredCheckedNodes);
      dispatch(
        newlazyTree.reducers.slice.actions.clearCheckedKeys({ treeKey }),
      );
      notification.success({ message: successMessage });
    } catch (err) {
      if (axios.isAxiosError(err)) {
        return rejectWithValue(err);
      }
      throw err;
    }

    // Если передан updateCallback - обновляем на фронте и не рефетчим ноды
    if (updateCallback) {
      dispatch(
        newlazyTree.reducers.slice.actions.updateNodes({
          treeKey,
          itemIds: filteredCheckedNodes.map((node) => node.itemId!),
          updateCallback,
        }),
      );
      return '';
    }

    // Получаем уникальные идентификаторы родителей выбранных узлов
    const parentIds = Array.from(
      new Set(filteredCheckedNodes.map((node) => node.parent).filter(Boolean)),
    ) as string[];

    Promise.all(
      parentIds.map((itemId) =>
        dispatch(
          getTreeThunk({
            endpoint,
            itemId,
            treeKey,
            isRefetch: true,
            queryParams,
            signal,
            transformResponse,
          }),
        ),
      ),
    );

    return '';
  },
);
