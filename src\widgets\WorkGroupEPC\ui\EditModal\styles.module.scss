@import 'src/app/styles/mixins';

.popup {
  height: min-content;
  width: 50%;
  min-width: 600px;
  gap: 0;
}

.transfer {
  display: flex;
  height: 100%;
  width: 100%;

  &Tree {
    height: 50vh;
    width: 55%;
    overflow: auto;
    @include scrollBar;
    @include virtualScrollbar;
  }

  &List {
    display: flex;
    flex-direction: column;
    gap: 5px;
    width: 45%;
    height: 50vh;
    overflow: auto;
    @include scrollBar;

    &Element {
      width: 100%;

      &Title {
        display: flex;
        align-items: center;
        gap: 5px;
      }
    }
  }
}

.divider {
  height: 100%;
}

.empty {
  margin: auto;
}
