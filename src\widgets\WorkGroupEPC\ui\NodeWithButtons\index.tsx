// NodeWithButtons.tsx
import { Skeleton, Typography } from 'antd';
import {
  EpcPermissions,
  FilesVisibility,
  LinkedData,
  WorkGroupEPCConfig,
} from 'widgets/WorkGroupEPC';

import { TreeActions } from 'features/NewLazyTree';
import {
  ChangeVisibilityButton,
  DeleteButton,
  DownloadButton,
  DownloadWithSignButton,
  MarkAsMainButton,
  ViewButton,
  ViewLinkedButton,
} from './buttons';

import styles from './styles.module.scss';

export const NodeWithButtons = ({
  node,
  title,
  togglePopup,
  handleUpdateLinked,
  deleteNode,
  refetchNode,
  saveStatus,
  epcPermissions,
}: {
  deleteNode: TreeActions['deleteNode'];
  epcPermissions: EpcPermissions;
  handleUpdateLinked: (body: LinkedData) => void;
  node: TreeElement;
  refetchNode: (itemId: string) => Promise<void>;
  saveStatus: FilesVisibility;
  title: React.ReactNode;
  togglePopup: (name: keyof typeof WorkGroupEPCConfig.popupsInitial) => void;
}): JSX.Element => {
  if (node.isSkeleton) {
    return (
      <div className={styles.root} style={{ width: `${node.width}%` || '60%' }}>
        <Skeleton active paragraph={false} className={styles.skeleton} />
      </div>
    );
  }

  return (
    <div className={styles.root} key={`buttons-${node.itemId}`}>
      <Typography.Text title="" key={`title-${node.itemId}`}>
        {title}
      </Typography.Text>
      <div className={styles.rootContainer}>
        {epcPermissions.canDownloadFiles && !node.isDirectory && (
          <DownloadButton
            canDownload={!!node.permissions?.canDownload}
            fileNetId={node.fileNetId!}
          />
        )}

        {epcPermissions.canDownloadFiles &&
          !node.isDirectory &&
          node.fileSignExist && (
            <DownloadWithSignButton
              canDownload={!!node.permissions?.canDownload}
              fileNetId={node.fileNetId!}
            />
          )}

        {epcPermissions.canViewFiles && !node.isDirectory && (
          <ViewButton
            fileNetId={node.fileNetId!}
            canView={!!node.permissions?.canView}
          />
        )}

        {node?.isMain === 0 && !node.isDirectory && epcPermissions.canEdit && (
          <MarkAsMainButton node={node} refetchNode={refetchNode} />
        )}

        {node.parent && (
          <ChangeVisibilityButton
            title={node.title as string}
            isDirectory={!!node.isDirectory}
            cabinetOnly={!!node.cabinetOnly}
            itemId={node.itemId!}
            canEdit={epcPermissions.canEdit}
            saveStatus={saveStatus}
          />
        )}

        {epcPermissions.canDeleteFiles && !node.isDirectory && (
          <DeleteButton node={node} deleteNode={deleteNode} />
        )}

        {node.isDirectory && (
          <ViewLinkedButton
            itemId={node.itemId!}
            title={node.title as string}
            hasLinked={!!node.hasLinked}
            canEdit={epcPermissions.canEdit}
            handleUpdateLinked={handleUpdateLinked}
            togglePopup={togglePopup}
          />
        )}
      </div>
    </div>
  );
};
