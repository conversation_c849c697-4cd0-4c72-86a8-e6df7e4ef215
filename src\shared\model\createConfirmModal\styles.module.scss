.container {
  padding: 10px;
  height: min-content;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  width: 400px;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 30px;
  word-break: break-word;

  &__buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
  }
}

.title.title {
  padding: 30px 20px 10px;
}

.divider {
  margin: 0 !important;
}

.popup.popup {
  gap: 0;
}

.disabledButton {
  cursor: auto !important;
}
