import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { LinkedData, WorkGroupEPCInitial } from 'widgets/WorkGroupEPC';
import { getDownloaderLink } from './actions';

export const initialState: WorkGroupEPCInitial = {
  epc: {
    buttons: { isPending: false, error: null },
    linkedData: { directoryId: '', watchLinked: [], editLinked: [], title: '' },
  },
};

export const slice = createSlice({
  name: 'workGroupEpc',
  initialState,
  reducers: {
    reset: () => ({ ...initialState }),
    handleUpdateLinked: (state, { payload }: PayloadAction<LinkedData>) => {
      state.epc.linkedData = payload;
    },
    handleResetLinked: (state) => {
      state.epc.linkedData.editLinked = state.epc.linkedData.watchLinked;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(getDownloaderLink.pending, (state) => {
      state.epc.buttons.isPending = true;
      state.epc.buttons.error = null;
    });

    builder.addCase(getDownloaderLink.rejected, (state, { error }) => {
      state.epc.buttons.isPending = false;
      state.epc.buttons.error = error;
    });

    builder.addCase(getDownloaderLink.fulfilled, (state) => {
      state.epc.buttons.isPending = false;
    });
  },
});
