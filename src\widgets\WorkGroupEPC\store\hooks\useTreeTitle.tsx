import { useCallback } from 'react';
import {
  EpcPermissions,
  FilesVisibility,
  ToggleEPCPopup,
  WorkGroupEPCStore,
} from 'widgets/WorkGroupEPC';
// eslint-disable-next-line import/no-internal-modules
import { NodeWithButtons } from 'widgets/WorkGroupEPC/ui/NodeWithButtons';
import { TreeActions } from 'features/NewLazyTree';
import { renderTreeTitle, useCreateSliceActions } from 'shared/model';

type UseTreeTitle = {
  deleteNode: TreeActions['deleteNode'];
  epcPermissions: EpcPermissions;
  findedItemId: string;
  refetchNode: TreeActions['refetchNode'];
  saveStatus: FilesVisibility;
  togglePopup: ToggleEPCPopup;
  loadMoreCatalogItems: (parentId: string) => Promise<void>;
};

export const useTreeTitle = ({
  deleteNode,
  refetchNode,
  saveStatus,
  findedItemId,
  togglePopup,
  epcPermissions,
  loadMoreCatalogItems,
}: UseTreeTitle): ((node: TreeElement) => JSX.Element) => {
  const { handleUpdateLinked } = useCreateSliceActions(
    WorkGroupEPCStore.reducers.slice.actions,
  );
  const renderTitle = useCallback(
    (node: TreeElement) => {
      // Обработка технического элемента "Загрузить еще"
      if (node.isLoadMoreButton) {
        return (
          <div
            style={{
              cursor: 'pointer',
              color: '#1890ff',
              padding: '4px 8px',
              borderRadius: '4px',
              transition: 'background-color 0.3s',
            }}
            onClick={() => {
              if (node.parentId) {
                loadMoreCatalogItems(node.parentId);
              }
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = '#f0f0f0';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            {node.title}
          </div>
        );
      }

      // Обычная обработка узлов
      return (
        <NodeWithButtons
          deleteNode={deleteNode}
          refetchNode={refetchNode}
          saveStatus={saveStatus}
          node={node}
          title={
            <div>
              {renderTreeTitle(
                node,
                node.key === findedItemId,
                node.hasLinked,
                false, // TODO Переписать renderTreeTitle
                true,
              )}
            </div>
          }
          togglePopup={togglePopup}
          handleUpdateLinked={handleUpdateLinked}
          epcPermissions={epcPermissions}
        />
      );
    },
    [
      deleteNode,
      refetchNode,
      saveStatus,
      findedItemId,
      togglePopup,
      handleUpdateLinked,
      epcPermissions,
      loadMoreCatalogItems,
    ],
  );
  return renderTitle;
};
