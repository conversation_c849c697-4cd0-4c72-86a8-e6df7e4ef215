import { ReactNode } from 'react';
import { WorkGroupEPCConfig } from 'widgets/WorkGroupEPC';
import { PermissionsInitial } from 'entities/Permissions';

export type ToggleEPCPopup = (
  name: keyof typeof WorkGroupEPCConfig.popupsInitial,
  status?: boolean | undefined,
) => void;

export type WorkGroupEPCProps = Omit<EPCInnerProps, 'height'> & {
  handleClose: Callback;
  isOpened: boolean;
};

export type TreeChecked = {
  keys: Key[];
  nodes: TreeElement[];
  selectedDirs: TreeElement[];
  selectedFiles: TreeElement[];
};

export interface EPCInnerProps {
  cabinetId: string;
  height: number;
  isFullFilesAccess: boolean;
  permissions: PermissionsInitial;
  isFullSize?: boolean;
}

export interface WorkGroupEPCInitial {
  epc: {
    buttons: ApiDefaultKeys;
    linkedData: LinkedData;
  };
}

export type EditLinked = Pick<
  TreeElement,
  'itemId' | 'title' | 'isDirectory'
>[];

export interface LinkedData {
  directoryId: string;
  editLinked: EditLinked;
  title: ReactNode;
  watchLinked: EditLinked;
}

export interface TreeMoveBody {
  itemId: string;
  parentId: string;
  position: number;
}

export type TogglePopup = (
  name: keyof typeof import('widgets/WorkGroupEPC').WorkGroupEPCConfig.popupsInitial,
) => void;

export type AdditionalFields = Record<
  'archive' | 'includeSigns' | 'unArchive',
  boolean
>;

export type DownloadSettingsFields = {
  key: keyof AdditionalFields;
  title: Title;
};

export interface DownloadBody {
  archive: boolean;
  directoryIds: Key[];
  extract: boolean;
  fileIds: Key[];
  includeSigns: boolean;
}

export interface EPCDownloadModalInnerProps {
  cabinetId: string;
  handleClose: Callback;
}

export type EPCDownloadModalProps = EPCDownloadModalInnerProps & {
  isOpened: boolean;
};

export type EpcPermissions = Record<
  | 'canViewFiles'
  | 'canDownloadFiles'
  | 'canDeleteFiles'
  | 'canEdit'
  | 'canEditEPCStructure'
  | 'canCopyFiles',
  boolean
>;

export type FilesVisibility = (
  fileIds: string[],
  directoryIds: string[],
  isCabinetOnly: boolean,
) => Promise<void>;

export type SaveFile = (
  type: 'copyFiles' | 'copyFilesWithCatalogs',
  body: { flat: boolean; items: Partial<TreeElement>[] },
) => Promise<void>;

export type SaveFilesResponse = string;
