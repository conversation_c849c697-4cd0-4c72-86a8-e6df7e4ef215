import { TreeState } from '../types';

/** Создает технический элемент "Загрузить еще" для каталога с пагинацией */
const createLoadMoreNode = (
  parentId: string,
  remainingCount: number,
  totalCount: number,
): TreeElement => ({
  itemId: `load-more-${parentId}`,
  key: `load-more-${parentId}`,
  title: `Загрузить еще ${remainingCount} из ${totalCount} элементов каталога`,
  isLeaf: true,
  isLoadMoreButton: true,
  parentId,
  disabled: false,
  isDirectory: false,
  isCallable: false,
  pos: `load-more-${parentId}`,
  childrenIds: [],
  checkable: false, // Отключаем чекбокс для технического элемента
});

/**
 * Функция для получения полного дерева в виде вложенной структуры.
 *
 * @returns Массив корневых узлов с вложенными дочерними узлами
 */

export const getTreeData = (
  entities: TreeState['entities'] | undefined,
  rootId: string | string[] | undefined,
): TreeElement[] => {
  if (!entities || Object.keys(entities!).length === 0 || !rootId) return [];

  // Рекурсивная функция для построения дерева
  const buildTree = (nodeIds: string[]): TreeElement[] =>
    nodeIds
      .map((id) => {
        const node = entities[id]!;
        if (!node) return null;

        const children =
          node.childrenIds && node.childrenIds.length > 0
            ? buildTree(node.childrenIds)
            : [];

        // Добавляем технический элемент "Загрузить еще" если есть еще элементы для загрузки
        const shouldShowLoadMore =
          node.isDirectory &&
          node.total &&
          children.length < node.total &&
          children.length > 0; // Показываем только если уже есть загруженные элементы

        if (shouldShowLoadMore) {
          const remainingCount = node.total! - children.length;
          const loadMoreNode = createLoadMoreNode(
            node.itemId!,
            remainingCount,
            node.total!,
          );
          children.push(loadMoreNode);
        }

        return { ...node, children };
      })
      .filter((node) => !!node);

  // Поддержка как одного корневого узла (строка), так и массива корневых узлов
  const rootIds = Array.isArray(rootId) ? rootId : [rootId];
  return buildTree(rootIds);
};
