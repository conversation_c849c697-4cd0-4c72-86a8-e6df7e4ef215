import axios from 'axios';
import { useCallback } from 'react';
import { SaveFile, SaveFilesResponse } from 'widgets/WorkGroupEPC';
import { apiUrls } from 'shared/api';
import { appErrorNotification, generateUrlWithQueryParams } from 'shared/lib';
import { useAxiosRequest } from 'shared/model';

const COPY_DEFAULT_ERROR = 'Ошибка при копировании файла';

export const useCopyFile = (
  cabinetId: string,
  parentId: string,
): [SaveFile, typeof stateSaveFiles] => {
  const [triggerFiles, stateSaveFiles] = useAxiosRequest<SaveFilesResponse>();

  const saveFiles = useCallback<SaveFile>(
    async (type, params) => {
      try {
        await triggerFiles(
          generateUrlWithQueryParams(apiUrls.workGroup.EPC.copyFiles, {
            cabinetId,
            parentId,
            flat: params.flat,
          }),
          {
            method: 'POST',
            data: params.items,
          },
        );
      } catch (err) {
        if (axios.isAxiosError(err)) {
          const errorMessage = err.response?.data?.error;
          appErrorNotification(errorMessage || COPY_DEFAULT_ERROR, err);
        } else {
          appErrorNotification(COPY_DEFAULT_ERROR, err as AppError);
        }
      }
    },
    [cabinetId, parentId, triggerFiles],
  );

  return [saveFiles, stateSaveFiles];
};
