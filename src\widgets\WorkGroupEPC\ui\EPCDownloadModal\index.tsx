import { Switch, Typography } from 'antd';
import { FC } from 'react';
import {
  EPCDownloadModalInnerProps,
  WorkGroupEPCConfig,
  WorkGroupEPCStore,
  EPCDownloadModalProps,
} from 'widgets/WorkGroupEPC';
import { newlazyTree } from 'features/NewLazyTree';
import { useAppSelector, useCreateSliceActions } from 'shared/model';
import { AppPopup, BorderedFieldset, ButtonsContainer } from 'shared/ui';

import styles from './styles.module.scss';

const EPCDownloadModalInner: FC<EPCDownloadModalInnerProps> = ({
  handleClose,
  cabinetId,
}) => {
  const { EPC_TREE_KEY } = WorkGroupEPCConfig;
  const { downloadSettingsFields } = WorkGroupEPCConfig;
  const { clearCheckedKeys } = useCreateSliceActions(
    newlazyTree.reducers.slice.actions,
  );
  const resetCheckboxes = (): void =>
    clearCheckedKeys({ treeKey: EPC_TREE_KEY });
  const loadedKeys = useAppSelector((state) =>
    newlazyTree.selectors.loadedKeysSelector(state, {
      treeKey: EPC_TREE_KEY,
    }),
  );
  const selectedDirs = useAppSelector((state) =>
    newlazyTree.selectors.checkedElementsSelector(state, {
      treeKey: EPC_TREE_KEY,
      filter: (node: TreeElement) => !!node.isDirectory,
    }),
  );
  const selectedFiles = useAppSelector((state) =>
    newlazyTree.selectors.checkedElementsSelector(state, {
      treeKey: EPC_TREE_KEY,
      filter: (node: TreeElement) => !node.isDirectory,
    }),
  );

  const [settings, setSettings, resetSettings] =
    WorkGroupEPCStore.hooks.useAdditionalSettings();

  const handleCloseAndReset: Callback = () => {
    resetSettings();
    handleClose();
  };

  const buttons = WorkGroupEPCStore.hooks.useDownloadButtons(
    selectedFiles,
    selectedDirs,
    loadedKeys,
    handleCloseAndReset,
    cabinetId,
    settings,
    resetCheckboxes,
  );

  return (
    <>
      <BorderedFieldset
        title="Настройки скачивания"
        containerClassName={styles.container}
      >
        <div className={styles.content}>
          {downloadSettingsFields.map((item) => (
            <div className={styles.settings} key={item.key}>
              <Typography.Text>{item.title}:</Typography.Text>
              <Switch
                onChange={(checked) => {
                  setSettings({ ...settings, [item.key]: checked });
                }}
                checked={settings[item.key]}
                disabled={item.key === 'archive' && settings.unArchive}
              />
            </div>
          ))}
        </div>
      </BorderedFieldset>

      <ButtonsContainer buttons={buttons} />
    </>
  );
};

export const EPCDownloadModal: FC<EPCDownloadModalProps> = ({
  isOpened,
  handleClose,
  ...props
}) => (
  <AppPopup
    className={styles.popup}
    isOpened={isOpened}
    onClose={handleClose}
    title="Скачать файлы с описью"
  >
    <EPCDownloadModalInner {...props} handleClose={handleClose} />
  </AppPopup>
);
