import { FilesVisibility } from 'widgets/WorkGroupEPC/types';
import { TreeActions } from 'features/NewLazyTree';

export const useVisibilityButtons = (
  actionOnCheckedNodes: TreeActions['actionOnCheckedNodes'],
  isVisible: boolean,
  handleClose: Callback,
  handleSave: FilesVisibility,
  isPending: boolean,
): AdditionalButton[] => [
  {
    title: 'Сохранить',
    key: 'save',
    loading: isPending,
    type: 'primary',
    onClick: async () => {
      await actionOnCheckedNodes({
        successMessage: 'Видимость файлов успешно изменена',
        filterCallback: (node: TreeElement) =>
          Object.hasOwn(node, 'parent') && node.cabinetOnly === !isVisible,
        updateCallback: (item) => {
          item.cabinetOnly = isVisible;
        },
        actionCallback: async (nodes: TreeElement[]) => {
          const directoryIds = nodes
            .filter((node) => node.isDirectory)
            .map(({ itemId }) => itemId!);
          const fileIds = nodes
            .filter((node) => !node.isDirectory)
            .map(({ itemId }) => itemId!);
          await handleSave(fileIds, directoryIds, isVisible);
        },
      });
      handleClose();
    },
  },
  {
    title: 'Отмена',
    danger: true,
    loading: isPending,
    ghost: true,
    key: 'cancel',
    onClick: handleClose,
  },
];
