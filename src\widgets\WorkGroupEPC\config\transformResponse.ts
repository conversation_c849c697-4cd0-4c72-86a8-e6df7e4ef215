import { AxiosResponse } from 'axios';
import { treeParserByConfig } from 'shared/lib';
import { treeParserConfig } from './treeParserConfig';

export const transformResponse = (
  response: AxiosResponse<{
    treeData: TreeElement[];
    pagination?: { total: number; page: number; size: number }
  }>,
  parentId?: string,
): TreeElement[] => {
  const nodes = treeParserByConfig(response.data.treeData, treeParserConfig) || [];

  // Если есть информация о пагинации, сохраняем её для использования в processTreeData
  // Информация о пагинации будет добавлена к родительскому узлу в processTreeData
  if (response.data.pagination && parentId) {
    // Добавляем информацию о пагинации к первому узлу для передачи в processTreeData
    // Это временное решение - информация будет перенесена к родительскому узлу
    if (nodes.length > 0) {
      const { total, page, size } = response.data.pagination;
      nodes[0].total = total;
      nodes[0].page = page;
      nodes[0].size = size;
    }
  }

  return nodes;
};
