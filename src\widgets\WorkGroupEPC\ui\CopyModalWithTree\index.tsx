import { Tree, TreeProps } from 'antd';
import { FC, useCallback, useMemo, useState, useEffect } from 'react';
import {
  WorkGroupEPCLib,
  EpcPermissions,
  WorkGroupEPCStore,
  WorkGroupEPCConfig,
} from 'widgets/WorkGroupEPC';
import { newlazyTree } from 'features/NewLazyTree';
import { renderTreeTitle, useAppSelector } from 'shared/model';
import { AppPopup, ButtonsContainer } from 'shared/ui';
import styles from './styles.module.scss';

type CopyModalWithTreeInnerProps = {
  cabinetId: string;
  epcPermissions: EpcPermissions;
  handleClose: Callback;
  loadData: TreeProps<TreeElement>['loadData'];
  refetchNode: (itemId: string) => void;
};

type CopyModalWithTreeProps = CopyModalWithTreeInnerProps & {
  isOpened: boolean;
};

const initialCheck = { key: [], itemId: '', isLoaded: false } as {
  itemId: string;
  key: Key[];
};

const CopyModalWithTreeInner: FC<CopyModalWithTreeInnerProps> = ({
  loadData,
  handleClose,
  refetchNode,
  cabinetId,
  epcPermissions,
}) => {
  const { EPC_TREE_KEY } = WorkGroupEPCConfig;

  const loadedKeysObj = useAppSelector((state) =>
    newlazyTree.selectors.loadedKeysObjSelector(state, {
      treeKey: EPC_TREE_KEY,
    }),
  );
  const loadedKeys = useAppSelector((state) =>
    newlazyTree.selectors.loadedKeysSelector(state, {
      treeKey: EPC_TREE_KEY,
    }),
  );
  const expandedKeysFromRedux = useAppSelector((state) =>
    newlazyTree.selectors.expandedKeysSelector(state, {
      treeKey: EPC_TREE_KEY,
    }),
  );

  const selectedFiles = useAppSelector((state) =>
    newlazyTree.selectors.checkedElementsSelector(state, {
      treeKey: EPC_TREE_KEY,
      filter: (node: TreeElement) => !node.isDirectory,
    }),
  );
  const checkedNodes = useAppSelector((state) =>
    newlazyTree.selectors.checkedElementsSelector(state, {
      treeKey: EPC_TREE_KEY,
      filter: () => true,
    }),
  );

  const tree = useAppSelector((state) =>
    newlazyTree.selectors.treeSelector(state, {
      treeKey: EPC_TREE_KEY,
    }),
  );

  const [expandedKeys, setExpandedKeysState] = useState<Key[]>(
    expandedKeysFromRedux,
  );

  useEffect(() => {
    setExpandedKeysState(expandedKeysFromRedux);
  }, [expandedKeysFromRedux]);

  const [check, setCheck] = useState(initialCheck);

  const parsedSelectedNodes = useMemo(
    () =>
      (checkedNodes as (TreeElement & { fullCopy: boolean })[]).map((node) => {
        if (!node.isDirectory) {
          return node;
        }

        return {
          ...node,
          fullCopy: !loadedKeysObj[node.key],
        };
      }),
    [loadedKeysObj, checkedNodes],
  );

  const parents = useMemo(() => {
    const uniqParents = new Set<string>(
      [...selectedFiles, ...checkedNodes]
        .filter((node) => node.parent)
        .map(({ parent }) => parent) as string[],
    );

    return uniqParents;
  }, [selectedFiles, checkedNodes]);

  const buttons = WorkGroupEPCStore.hooks.useCopyModalButtons(
    handleClose,
    parsedSelectedNodes,
    check.key.length === 0,
    () => {
      refetchNode(check.itemId);
    },
    epcPermissions,
    cabinetId,
    check.itemId,
  );

  const parsedTree = useMemo(
    () =>
      WorkGroupEPCLib.modifyAndFilterTreeByConfigs(
        tree,
        (node) => Boolean(node.isDirectory) && node.isFixed !== 2,
        (node) => {
          const fields: Partial<TreeElement> = {};
          const isLeaf =
            node.children &&
            node?.children?.length > 0 &&
            node?.children?.every(
              (item) => !item.isDirectory || item.isFixed === 2,
            );

          if (
            parents.has(node?.itemId || '') ||
            checkedNodes.some(({ itemId }) => itemId === node?.itemId || '')
          ) {
            fields.checkable = false;
            fields.isLeaf = true;
          }

          if (isLeaf) fields.isLeaf = true;
          return fields;
        },
      ),
    [parents, checkedNodes, tree],
  );

  const handleCheck = useCallback(
    (_: unknown, info: { node: TreeElement }): void => {
      setCheck(
        info.node.checked
          ? initialCheck
          : { key: [info.node.key], itemId: info.node?.itemId || '' },
      );
    },
    [],
  );

  const handleExpand = useCallback((keys: Key[]) => {
    setExpandedKeysState(keys);
  }, []);

  return (
    <div className={styles.content}>
      <Tree
        checkable
        selectable={false}
        loadedKeys={loadedKeys}
        checkedKeys={check.key}
        checkStrictly
        onCheck={handleCheck}
        treeData={parsedTree}
        expandedKeys={expandedKeys}
        loadData={loadData}
        onExpand={handleExpand}
        height={400}
        showLine
        titleRender={renderTreeTitle}
      />
      <ButtonsContainer buttons={buttons} />
    </div>
  );
};

export const CopyModalWithTree: FC<CopyModalWithTreeProps> = ({
  isOpened,
  handleClose,
  ...props
}) => (
  <AppPopup
    isOpened={isOpened}
    onClose={handleClose}
    title="Вставить файлы"
    className={styles.popup}
  >
    <CopyModalWithTreeInner {...props} handleClose={handleClose} />
  </AppPopup>
);
