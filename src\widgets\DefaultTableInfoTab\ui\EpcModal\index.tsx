import { Tree } from 'antd';
import { FC, useCallback } from 'react';
import { useMeasure } from 'react-use';
import AutoSizer from 'react-virtualized-auto-sizer';
import {
  EpcModalInnerProps,
  EpcModalProps,
  defaultTableInfoTabConfig,
  defaultTableInfoTabStore,
} from 'widgets/DefaultTableInfoTab';
import { SearchDrawer } from 'features/SearchDrawer';
import { collectNodesWithChildren } from 'shared/lib';
import {
  renderTreeTitle,
  usePopupsToggle,
  useTreeSearchArrow,
} from 'shared/model';
import {
  AppPopup,
  ApiContainer,
  ButtonsContainer,
  TreeSearch,
} from 'shared/ui';
import { epcNode } from '../EpcNode';

import styles from './styles.module.scss';

const EpcModalInner: FC<EpcModalInnerProps> = ({
  error,
  onLoadData,
  epcTree,
  foundLinkKey,
  isPending,
  refresh,
  searchEpcTree,
  values,
  setValues,
  height = 500,
  loadedKeys,
  setLoadedKeys,
  autoExpandParent,
  expandedKeys,
  handleExpand,
  handleAutoExpand,
  clearSearchTags,
}) => {
  const { formFields, popupsState } = defaultTableInfoTabConfig;

  const [popup, togglePopup] = usePopupsToggle(popupsState);
  const toggleSearch = useCallback(() => togglePopup('search'), [togglePopup]);

  const additionalSelects =
    defaultTableInfoTabStore.hooks.useGetSearchSelects();

  const [treeRef, taggedNodes, currentIndex, foundKey, arrowUp, arrowDown] =
    useTreeSearchArrow(epcTree, '', handleAutoExpand);

  const [buttonsRef, { height: buttonsHeight }] = useMeasure<HTMLDivElement>();
  const treeHeight = height - buttonsHeight;

  return (
    <div className={styles.epcModalInner} style={{ height }}>
      <ApiContainer isPending={isPending} error={error}>
        <Tree
          className={styles.tree}
          loadedKeys={loadedKeys}
          ref={treeRef}
          height={treeHeight}
          treeData={epcTree}
          loadData={onLoadData}
          titleRender={(node) =>
            epcNode(
              renderTreeTitle(
                node,
                node.key === foundKey,
                node.hasLinked,
                node.key === foundLinkKey,
              ),
              node,
            )
          }
          showLine
          defaultExpandAll
          onExpand={handleExpand}
          autoExpandParent={autoExpandParent}
          expandedKeys={expandedKeys}
        />
      </ApiContainer>

      <div className={styles.buttons} ref={buttonsRef}>
        <ButtonsContainer
          buttons={[
            {
              key: 'refresh',
              title: 'Обновить',
              onClick: () => {
                refresh();
                handleExpand([]);
                setLoadedKeys([]);
              },
              type: 'primary',
              disabled: isPending,
            },
          ]}
        />

        <TreeSearch
          togglePopup={toggleSearch}
          treeSearchConfig={{
            arrowUp: () => {
              arrowUp();
            },
            arrowDown: () => {
              arrowDown();
            },
            taggedNodes,
            currentIndex,
          }}
        />
      </div>

      <SearchDrawer
        width={550}
        placement="right"
        handleClose={toggleSearch}
        isOpened={popup.search}
        formFields={[...formFields, ...additionalSelects]}
        values={values}
        setValues={setValues}
        handleResetSearch={taggedNodes.length > 0 ? clearSearchTags : undefined}
        searchCallback={async (body) => {
          const searchBody =
            'marks' in body ? { ...body, isMainFile: true } : body;
          const prevLoadedKeys = [...loadedKeys];

          setLoadedKeys([]);
          handleExpand([]);

          const res = await searchEpcTree(searchBody);
          if (res) {
            const keys = collectNodesWithChildren(res).map((node) => node.key);
            setLoadedKeys(keys);
            handleExpand(keys);
          } else {
            setLoadedKeys(prevLoadedKeys);
            handleExpand(prevLoadedKeys);
          }

          toggleSearch();
        }}
      />
    </div>
  );
};

export const EpcModal: FC<EpcModalProps> = ({
  isOpened,
  handleClose,
  ...props
}) => (
  <AppPopup
    className={styles.popup}
    isOpened={isOpened}
    onClose={handleClose}
    title="Электронный паспорт проверки"
  >
    <AutoSizer disableWidth className={styles.autoSizer}>
      {({ height }) => <EpcModalInner {...props} height={height} />}
    </AutoSizer>
  </AppPopup>
);
